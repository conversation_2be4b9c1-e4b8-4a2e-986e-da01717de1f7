import {Component, OnInit, signal, WritableSignal} from '@angular/core';

import {DynamicBaseInputComponent} from './dynamic-baseinput.component';
import {ObjectsUtils} from "src/app/utils";
import { Instance } from '../../smart-creation/models/smart-creation-validation.types';
import { BadgeType} from '../../search/endpoint/search.model';


@Component({
  selector: 'dynamic-input-table',
  template: `
    <p-messages *ngIf="this.data()?.length < 1" severity="info">
      <ng-template pTemplate>
        {{ 'layout.item-details.no-data' | translate }}
      </ng-template>
    </p-messages>
    @if (data()?.length > 0 && columns() && labels()) {
      <p-table [value]="data()" [tableStyle]="{'min-width': '50rem'}" [columns]="columns()"
               dataKey="idx"
               [expandedRowKeys]="expandedRowKeys()"
               styleClass="p-datatable-compat plant-table">
        <ng-template pTemplate="header" let-columns>
          <tr>
            <th class="w-3rem" *ngIf="columns?.length > 5"></th>
            <th *ngFor="let column of columns | slice:0:5; let i=index">
              <ng-container *ngIf="this.labels()">
                {{ this.labels()[column.header] | translate | attributeNameTranslate }}
              </ng-container>
            </th>
            <th [class]="getActionColumnWidth()" *ngIf="shouldShowActionColumn()">
              {{ getActionColumnHeader() | translate }}
            </th>
        </tr>
        </ng-template>
        <ng-template pTemplate="body" let-details let-ri="rowIndex" let-expanded="expanded">
          <tr [class.odd-tbl-row]="details.rowIndex| isOdd">
            <td *ngIf="columns()?.length > 5">
              <button type="button" pButton [pRowToggler]="details"
                      [text]="true"
                      [rounded]="true"
                      [plain]="true"
                      size="small"
                      [icon]="expanded ? 'fas fa-chevron-down' : 'pi pi-chevron-right'"></button>
            </td>
            <td *ngFor="let column of columns() | slice:0:5; let i=index"
                [dynamicValueTranslate]="details[column.field]"
                [locale]="locale()"
                [fieldName]="column.field"
                [fieldConfig]="column?.fieldConfig"
                [currentClient]="clientId"
                useTranslatePipe="autodetect"
            >
            </td>
            <td [class]="getActionColumnWidth()" *ngIf="shouldShowActionColumn()">
              <!-- Custom template content -->
              <ng-container *ngIf="componentProps.actionColumnTemplate">
                <ng-container *ngTemplateOutlet="componentProps.actionColumnTemplate; context: getActionColumnContext(details, ri)">
                </ng-container>
              </ng-container>
              <!-- Legacy crossPlantInstances logic for backward compatibility -->
              <ng-container *ngIf="!componentProps.actionColumnTemplate && shouldShowLegacyBadge(ri)">
                <app-card-badge
                  [badgeType]="this.badgeType.PROCESSES"
                  [material]="{uuid: this.crossPlantInstances[ri].materialId}"
                  *ngIf="this.crossPlantInstances[ri].inProgressProcessCount > 0"
                  [value]="this.crossPlantInstances[ri].inProgressProcessCount"
                  (click)="onClickHandler(this.crossPlantInstances[ri])"
                ></app-card-badge>
              </ng-container>
            </td>
        </tr>
        </ng-template>
        <ng-template pTemplate="rowexpansion" let-details>
          <tr [class.odd-tbl-row]="details.rowIndex| isOdd">
            <td>&nbsp;</td>
            <td colspan="5" class="px-2">
              <div class="label-top flex flex-wrap gap-2 justify-content-between">
                @for (column of columns(); track column) {
                  <div class="sc-form-input flex-1 w-min-3">
                    <div class="sc-form-input-info">
                      <div class="sc-form-input-label">
                      <span
                          class="col-form-label font-semibold text-nowrap">{{ this.labels()?.[column.header] | translate | attributeNameTranslate }}</span>
                      </div>
                      <div class="sc-form-input-core">
                      </div>
                    </div>
                    <div class="sc-form-input-control">
                    <span class="p-input-readonly ellipsis" [dynamicValueTranslate]="details[column.field]"
                          [fieldName]="column.field"
                          [fieldConfig]="column?.fieldConfig"
                          [currentClient]="clientId"
                          [locale]="locale()"
                          useTranslatePipe="autodetect">
                    </span>
                    </div>

                  </div>
                }
              </div>
            </td>
          </tr>
        </ng-template>
      </p-table>
    }
    
  `,
  // changeDetection: ChangeDetectionStrategy.OnPush
})


export class DynamicInputTableComponent extends DynamicBaseInputComponent implements OnInit {

  data: WritableSignal<any[]> = signal<any[]>([]);
  columns: WritableSignal<any[]> = signal<any[]>([]);
  labels: WritableSignal<any> = signal<any>({});
  expandedRowKeys: WritableSignal<any> = signal<any>({});
  crossPlantInstances: Instance[] = [];
  badgeType = BadgeType;

  ngOnInit() {
    this.generateLabels();
    this.generateColumns();
    this.generateRows();
    // crossPlantInstances now managed via componentProps.instances
    this.crossPlantInstances = this.componentProps.instances?.length > 0 ? this.componentProps.instances : [];
  }

  generateLabels() {
    this.labels.set(this.componentProps.tableFields.reduce((label, field) => {
      label[field] = field;
      return label;
    }, {}));
  }

  generateColumns() {
    this.columns.set(this.componentProps.tableFields?.map((field: string) => ({field,
      header: field,
      fieldConfig: this.children?.find(c => c.id === field)
    })));
  }

  generateRows() {
    // try {
    if (ObjectsUtils.isNoU(this.componentProps?.value)) {
      return;
    }

    const origin: any[] = JSON.parse(this.componentProps.value) as any[];
    this.data.set(origin?.map((r: any[], idx) => {
      const mRow = { idx: `rowIdx-${idx}`, rowIndex: idx };
      this.componentProps?.tableFields?.forEach((fName, i) => {
        mRow[fName] = r?.[i];
      });

      return mRow;
    }));
  }
  onClickHandler(instance: Instance){
    this.store.dispatch({
            type: 'popups/open-material-history-in-progress-popup',
            payload: {
                materialId: instance.materialId
            }
    });
  }

  /**
   * Determines if the action column should be shown
   * @returns true if action column should be displayed
   */
  shouldShowActionColumn(): boolean {
    // Show if explicitly configured to show action column
    if (this.componentProps?.showActionColumn) {
      return true;
    }

    // Show if custom template is provided
    if (this.componentProps?.actionColumnTemplate) {
      return true;
    }

    // Show if legacy action column is enabled and instances exist
    if (this.componentProps?.useLegacyActionColumn && this.crossPlantInstances.length > 0) {
      return true;
    }

    // Legacy fallback: show if crossPlantInstances exist (for backward compatibility)
    return this.crossPlantInstances.length > 0;
  }

  /**
   * Gets the CSS width class for the action column
   * @returns CSS class string for column width
   */
  getActionColumnWidth(): string {
    return this.componentProps?.actionColumnWidth || 'w-3rem';
  }

  /**
   * Gets the header text for the action column
   * @returns Header text string
   */
  getActionColumnHeader(): string {
    return this.componentProps?.actionColumnHeader || '';
  }

  /**
   * Creates the context object passed to the action column template
   * @param rowData - The row data object
   * @param rowIndex - The row index
   * @returns Context object for template
   */
  getActionColumnContext(rowData: any, rowIndex: number): any {
    return {
      $implicit: rowData,
      rowData,
      rowIndex,
      crossPlantInstance: this.crossPlantInstances[rowIndex] || null,
      onClickHandler: (instance: Instance) => this.onClickHandler(instance)
    };
  }

  /**
   * Determines if the legacy badge should be shown for a specific row
   * @param rowIndex - The row index
   * @returns true if legacy badge should be displayed
   */
  shouldShowLegacyBadge(rowIndex: number): boolean {
    // Show legacy badge if configured to use legacy behavior and instances exist
    return this.componentProps?.useLegacyActionColumn &&
           this.crossPlantInstances.length > 0 &&
           !!this.crossPlantInstances[rowIndex];
  }

}
