import {Component, OnInit, signal, WritableSignal} from '@angular/core';

import {DynamicBaseInputComponent} from './dynamic-baseinput.component';
import {ObjectsUtils} from 'src/app/utils';

@Component({
  selector: 'dynamic-input-table',
  template: `
    <p-messages *ngIf="this.data()?.length < 1" severity="info">
      <ng-template pTemplate>
        {{ 'layout.item-details.no-data' | translate }}
      </ng-template>
    </p-messages>
    @if (data()?.length > 0 && columns() && labels()) {
      <p-table [value]="data()" [tableStyle]="{'min-width': '50rem'}" [columns]="columns()"
               dataKey="idx"
               [expandedRowKeys]="expandedRowKeys()"
               styleClass="p-datatable-compat plant-table">
        <ng-template pTemplate="header" let-columns>
          <tr>
            <th class="w-3rem" *ngIf="columns?.length > 5"></th>
            <th *ngFor="let column of columns | slice:0:5; let i=index">
              <ng-container *ngIf="this.labels()">
                {{ this.labels()[column.header] | translate | attributeNameTranslate }}
              </ng-container>
            </th>
            <th [class]="'w-3rem'" *ngIf="shouldShowActionColumn()"></th>
        </tr>
        </ng-template>
        <ng-template pTemplate="body" let-details let-ri="rowIndex" let-expanded="expanded">
          <tr [class.odd-tbl-row]="details.rowIndex| isOdd">
            <td *ngIf="columns()?.length > 5">
              <button type="button" pButton [pRowToggler]="details"
                      [text]="true"
                      [rounded]="true"
                      [plain]="true"
                      size="small"
                      [icon]="expanded ? 'fas fa-chevron-down' : 'pi pi-chevron-right'"></button>
            </td>
            <td *ngFor="let column of columns() | slice:0:5; let i=index"
                [dynamicValueTranslate]="details[column.field]"
                [locale]="locale()"
                [fieldName]="column.field"
                [fieldConfig]="column?.fieldConfig"
                [currentClient]="clientId"
                useTranslatePipe="autodetect"
            >
            </td>
            <td [class]="'w-3rem'" *ngIf="shouldShowActionColumn()">
              <ng-container *ngIf="componentProps.actionColumnTable?.actionColumnComponent && !componentProps.actionColumnTable?.actionColumnTemplate">
                <ng-container *ngComponentOutlet="componentProps.actionColumnTable?.actionColumnComponent; inputs: getActionColumnInputs(details, ri)">
                </ng-container>
              </ng-container>
            </td>
        </tr>
        </ng-template>
        <ng-template pTemplate="rowexpansion" let-details>
          <tr [class.odd-tbl-row]="details.rowIndex| isOdd">
            <td>&nbsp;</td>
            <td colspan="5" class="px-2">
              <div class="label-top flex flex-wrap gap-2 justify-content-between">
                @for (column of columns(); track column) {
                  <div class="sc-form-input flex-1 w-min-3">
                    <div class="sc-form-input-info">
                      <div class="sc-form-input-label">
                      <span
                          class="col-form-label font-semibold text-nowrap">{{ this.labels()?.[column.header] | translate | attributeNameTranslate }}</span>
                      </div>
                      <div class="sc-form-input-core">
                      </div>
                    </div>
                    <div class="sc-form-input-control">
                    <span class="p-input-readonly ellipsis" [dynamicValueTranslate]="details[column.field]"
                          [fieldName]="column.field"
                          [fieldConfig]="column?.fieldConfig"
                          [currentClient]="clientId"
                          [locale]="locale()"
                          useTranslatePipe="autodetect">
                    </span>
                    </div>

                  </div>
                }
              </div>
            </td>
          </tr>
        </ng-template>
      </p-table>
    }
  `,
  // changeDetection: ChangeDetectionStrategy.OnPush
})


export class DynamicInputTableComponent extends DynamicBaseInputComponent implements OnInit {

  data: WritableSignal<any[]> = signal<any[]>([]);
  columns: WritableSignal<any[]> = signal<any[]>([]);
  labels: WritableSignal<any> = signal<any>({});
  expandedRowKeys: WritableSignal<any> = signal<any>({});

  ngOnInit() {
    this.generateLabels();
    this.generateColumns();
    this.generateRows();
  }

  generateLabels() {
    this.labels.set(this.componentProps.tableFields.reduce((label, field) => {
      label[field] = field;
      return label;
    }, {}));
  }

  generateColumns() {
    this.columns.set(this.componentProps.tableFields?.map((field: string) => ({field,
      header: field,
      fieldConfig: this.children?.find(c => c.id === field)
    })));
  }

  generateRows() {
    // try {
    if (ObjectsUtils.isNoU(this.componentProps?.value)) {
      return;
    }

    const origin: any[] = JSON.parse(this.componentProps.value) as any[];
    this.data.set(origin?.map((r: any[], idx) => {
      const mRow = { idx: `rowIdx-${idx}`, rowIndex: idx };
      this.componentProps?.tableFields?.forEach((fName, i) => {
        mRow[fName] = r?.[i];
      });

      return mRow;
    }));
  }

  shouldShowActionColumn(): boolean {
    if (this.componentProps?.actionColumnTable?.showActionColumn) {
      return true;
    }
    if (this.componentProps?.actionColumnTable?.actionColumnTemplate) {
      return true;
    }
    if (this.componentProps?.actionColumnTable?.actionColumnComponent) {
      return true;
    }
    return false;
  }

  getActionColumnInputs(rowData: any, rowIndex: number): any {
    const baseInputs = {
      rowData,
      rowIndex
    };
    if (this.componentProps?.actionColumnTable?.actionColumnData && this.componentProps.actionColumnTable?.actionColumnData[rowIndex]) {
      baseInputs['actionData'] = this.componentProps.actionColumnTable?.actionColumnData[rowIndex];
    }
    return baseInputs;
  }

}
