# Migration Guide: Converting Hardcoded Action Columns to Dynamic Configuration

## Overview

This guide helps you migrate from hardcoded action columns in `DynamicInputTableComponent` to the new configurable action column system.

## Before: Hardcoded Action Column

### Problem
Previously, action columns were hardcoded directly in the component template:

```typescript
// OLD - Hardcoded in component template
<td *ngIf="this.crossPlantInstances.length > 0">
  <app-card-badge
    [badgeType]="this.badgeType.PROCESSES"
    [material]="{uuid: this.crossPlantInstances[ri].materialId}"
    *ngIf="this.crossPlantInstances[ri].inProgressProcessCount > 0"
    [value]="this.crossPlantInstances[ri].inProgressProcessCount"
    (click)="onClickHandler(this.crossPlantInstances[ri])"
  ></app-card-badge>
</td>
```

### Issues with Old Approach
- **Not reusable**: Each use case required modifying the component
- **Breaks encapsulation**: Business logic mixed with presentation
- **Hard to maintain**: Changes required component-level modifications
- **Not flexible**: Couldn't support different action types

## After: Dynamic Action Column

### Solution
The new approach uses configurable templates:

```typescript
// NEW - Configurable via props and templates
@Component({
  template: `
    <dynamic-input-table
      [componentProps]="tableProps"
      [clientId]="clientId"
      page="myPage">
    </dynamic-input-table>

    <ng-template #badgeActionTemplate let-rowData="rowData" let-rowIndex="rowIndex" let-crossPlantInstance="crossPlantInstance" let-onClickHandler="onClickHandler">
      <app-card-badge
        [badgeType]="badgeType.PROCESSES"
        [material]="{uuid: crossPlantInstance?.materialId}"
        *ngIf="crossPlantInstance?.inProgressProcessCount > 0"
        [value]="crossPlantInstance.inProgressProcessCount"
        (click)="onClickHandler(crossPlantInstance)">
      </app-card-badge>
    </ng-template>
  `
})
export class MyComponent {
  @ViewChild('badgeActionTemplate', { static: true }) badgeActionTemplate!: TemplateRef<any>;
  
  badgeType = BadgeType;

  get tableProps(): DynamicComponentProps {
    return {
      id: 'my-table',
      value: JSON.stringify(this.data),
      tableFields: this.fields,
      showActionColumn: true,
      actionColumnTemplate: this.badgeActionTemplate,
      actionColumnHeader: 'Processes',
      actionColumnWidth: 'w-4rem',
      instances: this.crossPlantInstances // Pass instances via props
    };
  }
}
```

## Step-by-Step Migration

### Step 1: Identify Current Usage
Find components using `DynamicInputTableComponent` with hardcoded action columns:

```bash
# Search for hardcoded action usage
grep -r "crossPlantInstances" --include="*.ts" --include="*.html" src/
```

### Step 2: Create Template
Move the hardcoded action content to an ng-template:

```typescript
// Before: Hardcoded in DynamicInputTableComponent
<td *ngIf="this.crossPlantInstances.length > 0">
  <app-card-badge ...></app-card-badge>
</td>

// After: Template in your component
<ng-template #actionTemplate let-crossPlantInstance="crossPlantInstance" let-onClickHandler="onClickHandler">
  <app-card-badge
    [badgeType]="badgeType.PROCESSES"
    [material]="{uuid: crossPlantInstance?.materialId}"
    *ngIf="crossPlantInstance?.inProgressProcessCount > 0"
    [value]="crossPlantInstance.inProgressProcessCount"
    (click)="onClickHandler(crossPlantInstance)">
  </app-card-badge>
</ng-template>
```

### Step 3: Update Component Props
Configure the action column via `DynamicComponentProps`:

```typescript
@ViewChild('actionTemplate', { static: true }) actionTemplate!: TemplateRef<any>;

get tableProps(): DynamicComponentProps {
  return {
    // ... existing props
    showActionColumn: true,
    actionColumnTemplate: this.actionTemplate,
    actionColumnHeader: 'Actions', // Optional
    actionColumnWidth: 'w-4rem',   // Optional
    instances: this.crossPlantInstances // Pass data via props
  };
}
```

### Step 4: Handle Data Access
Pass required data through component props instead of relying on component internals:

```typescript
// Before: Data accessed directly in component
this.crossPlantInstances = this.componentProps.instances;

// After: Data passed via context to template
getActionColumnContext(rowData: any, rowIndex: number): any {
  return {
    // ... standard context
    crossPlantInstance: this.crossPlantInstances[rowIndex] || null,
    onClickHandler: (instance: Instance) => this.onClickHandler(instance)
  };
}
```

## Common Migration Patterns

### Pattern 1: Badge Actions
```typescript
// Template for badge-based actions
<ng-template #badgeTemplate let-crossPlantInstance="crossPlantInstance" let-onClickHandler="onClickHandler">
  <app-card-badge
    [badgeType]="badgeType.PROCESSES"
    [material]="{uuid: crossPlantInstance?.materialId}"
    *ngIf="crossPlantInstance?.inProgressProcessCount > 0"
    [value]="crossPlantInstance.inProgressProcessCount"
    (click)="onClickHandler(crossPlantInstance)">
  </app-card-badge>
</ng-template>
```

### Pattern 2: Button Actions
```typescript
// Template for button-based actions
<ng-template #buttonTemplate let-rowData="rowData" let-rowIndex="rowIndex">
  <div class="flex gap-2">
    <p-button 
      icon="pi pi-eye" 
      size="small" 
      (click)="viewDetails(rowData)">
    </p-button>
    <p-button 
      icon="pi pi-pencil" 
      size="small" 
      (click)="editRow(rowData)">
    </p-button>
  </div>
</ng-template>
```

### Pattern 3: Conditional Actions
```typescript
// Template with conditional rendering
<ng-template #conditionalTemplate let-rowData="rowData" let-crossPlantInstance="crossPlantInstance">
  <ng-container *ngIf="shouldShowAction(rowData)">
    <app-custom-action 
      [data]="rowData"
      [instance]="crossPlantInstance"
      (actionClick)="handleAction($event)">
    </app-custom-action>
  </ng-container>
</ng-template>
```

## Testing Your Migration

### 1. Verify Backward Compatibility
Ensure existing functionality still works:

```typescript
// Test that legacy behavior is preserved
it('should maintain legacy crossPlantInstances behavior', () => {
  component.crossPlantInstances = [mockInstance];
  expect(component.shouldShowActionColumn()).toBeTruthy();
});
```

### 2. Test New Functionality
Verify the new template system works:

```typescript
// Test custom template rendering
it('should render custom action template', () => {
  component.componentProps.actionColumnTemplate = mockTemplate;
  fixture.detectChanges();
  
  const actionElements = fixture.debugElement.queryAll(By.css('.custom-action'));
  expect(actionElements.length).toBeGreaterThan(0);
});
```

### 3. Integration Testing
Test the complete flow:

```typescript
// Test template context and event handling
it('should pass correct context to template and handle events', () => {
  const spy = spyOn(component, 'handleAction');
  
  // Trigger action in template
  const actionButton = fixture.debugElement.query(By.css('.action-button'));
  actionButton.nativeElement.click();
  
  expect(spy).toHaveBeenCalledWith(expectedData);
});
```

## Benefits After Migration

### ✅ Improved Maintainability
- Action logic separated from table component
- Each use case manages its own actions
- Easier to modify and extend

### ✅ Better Reusability
- Same table component supports different action types
- Templates can be shared across components
- No need to modify core component for new use cases

### ✅ Enhanced Flexibility
- Multiple action types in same application
- Conditional action rendering
- Custom styling and behavior per use case

### ✅ Cleaner Architecture
- Separation of concerns
- Component encapsulation
- Testable action logic

## Troubleshooting

### Issue: Template Not Rendering
**Solution**: Ensure `@ViewChild` uses `static: true` for templates used in component properties.

### Issue: Context Data Missing
**Solution**: Verify data is passed through `componentProps` and accessed via template context.

### Issue: Events Not Working
**Solution**: Check that event handlers are properly bound in template and component.

### Issue: Styling Issues
**Solution**: Use consistent PrimeNG CSS classes and verify column width configuration.
