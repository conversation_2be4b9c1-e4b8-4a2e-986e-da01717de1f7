import { Component, TemplateRef, ViewChild } from '@angular/core';
import { DynamicComponentProps } from '../dynamic-components.models';

/**
 * Test component to verify that the legacy badge functionality works correctly
 * after the refactoring to move logic from component to factory
 */
@Component({
  selector: 'app-test-legacy-badge',
  template: `
    <div class="p-4">
      <h3>Test Legacy Badge Functionality</h3>
      
      <!-- Test TAM_CrossPlantAggregatedData with legacy badge -->
      <div class="mb-4">
        <h4>TAM_CrossPlantAggregatedData (Should show legacy badge)</h4>
        <dynamic-input-table
          [componentProps]="legacyBadgeProps"
          [clientId]="'TEST_CLIENT'"
          page="test">
        </dynamic-input-table>
      </div>

      <!-- Test with custom template -->
      <div class="mb-4">
        <h4>Custom Template (Should show custom content)</h4>
        <dynamic-input-table
          [componentProps]="customTemplateProps"
          [clientId]="'TEST_CLIENT'"
          page="test">
        </dynamic-input-table>
      </div>

      <!-- Custom action template -->
      <ng-template #customActionTemplate let-rowData="rowData" let-crossPlantInstance="crossPlantInstance">
        <div class="custom-action">
          <span>Custom Action for: {{ crossPlantInstance?.materialId }}</span>
        </div>
      </ng-template>
    </div>
  `
})
export class TestLegacyBadgeComponent {
  
  // Mock data for testing
  private mockInstances = [
    { materialId: 'MAT001', inProgressProcessCount: 3 },
    { materialId: 'MAT002', inProgressProcessCount: 1 }
  ];

  private mockTableData = [
    ['Material 1', 'Description 1', '100'],
    ['Material 2', 'Description 2', '200']
  ];

  private mockTableFields = ['name', 'description', 'quantity'];

  // Configuration that should trigger legacy badge rendering
  get legacyBadgeProps(): DynamicComponentProps {
    return {
      id: 'TAM_CrossPlantAggregatedData',
      value: JSON.stringify(this.mockTableData),
      tableFields: this.mockTableFields,
      instances: this.mockInstances,
      showActionColumn: true,
      useLegacyActionColumn: true,
      actionColumnHeader: '',
      actionColumnWidth: 'w-3rem'
    };
  }

  // Configuration with custom template
  get customTemplateProps(): DynamicComponentProps {
    return {
      id: 'custom-table',
      value: JSON.stringify(this.mockTableData),
      tableFields: this.mockTableFields,
      instances: this.mockInstances,
      showActionColumn: true,
      actionColumnTemplate: this.customActionTemplate,
      actionColumnHeader: 'Custom Actions',
      actionColumnWidth: 'w-6rem'
    };
  }

  @ViewChild('customActionTemplate', { static: true }) customActionTemplate!: TemplateRef<any>;
}
