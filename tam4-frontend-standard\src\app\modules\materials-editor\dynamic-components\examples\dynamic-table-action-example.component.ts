import { Component, TemplateRef, ViewChild } from '@angular/core';
import { DynamicComponentProps } from '../dynamic-components.models';

/**
 * Example component demonstrating how to use the DynamicInputTableComponent
 * with a custom action column template
 */
@Component({
  selector: 'app-dynamic-table-action-example',
  template: `
    <div class="p-4">
      <h3>Dynamic Input Table with Custom Action Column Example</h3>
      
      <!-- Example 1: Using custom template -->
      <div class="mb-4">
        <h4>Example 1: Custom Action Template</h4>
        <dynamic-input-table
          [componentProps]="customActionProps"
          [clientId]="'TEST_CLIENT'"
          page="example">
        </dynamic-input-table>
      </div>

      <!-- Example 2: Using showActionColumn flag only -->
      <div class="mb-4">
        <h4>Example 2: Simple Action Column</h4>
        <dynamic-input-table
          [componentProps]="simpleActionProps"
          [clientId]="'TEST_CLIENT'"
          page="example">
        </dynamic-input-table>
      </div>

      <!-- Example 3: Legacy behavior (no action column config) -->
      <div class="mb-4">
        <h4>Example 3: Legacy Behavior</h4>
        <dynamic-input-table
          [componentProps]="legacyProps"
          [clientId]="'TEST_CLIENT'"
          page="example">
        </dynamic-input-table>
      </div>

      <!-- Template for custom action column -->
      <ng-template #customActionTemplate let-rowData="rowData" let-rowIndex="rowIndex" let-onClickHandler="onClickHandler">
        <div class="flex gap-2">
          <p-button 
            icon="pi pi-eye" 
            size="small" 
            [text]="true"
            (click)="viewDetails(rowData, rowIndex)"
            pTooltip="View Details">
          </p-button>
          <p-button 
            icon="pi pi-pencil" 
            size="small" 
            [text]="true"
            (click)="editRow(rowData, rowIndex)"
            pTooltip="Edit Row">
          </p-button>
          <p-button 
            icon="pi pi-trash" 
            size="small" 
            [text]="true"
            severity="danger"
            (click)="deleteRow(rowData, rowIndex)"
            pTooltip="Delete Row">
          </p-button>
        </div>
      </ng-template>
    </div>
  `
})
export class DynamicTableActionExampleComponent {
  @ViewChild('customActionTemplate', { static: true }) customActionTemplate!: TemplateRef<any>;

  // Example data for demonstration
  private sampleData = [
    ['Item 1', 'Description 1', '100', 'Active'],
    ['Item 2', 'Description 2', '200', 'Inactive'],
    ['Item 3', 'Description 3', '300', 'Active']
  ];

  private sampleTableFields = ['name', 'description', 'quantity', 'status'];

  // Configuration for custom action template
  get customActionProps(): DynamicComponentProps {
    return {
      id: 'custom-action-example',
      value: JSON.stringify(this.sampleData),
      tableFields: this.sampleTableFields,
      showActionColumn: true,
      actionColumnTemplate: this.customActionTemplate,
      actionColumnHeader: 'Actions',
      actionColumnWidth: 'w-8rem'
    };
  }

  // Configuration for simple action column (no template)
  get simpleActionProps(): DynamicComponentProps {
    return {
      id: 'simple-action-example',
      value: JSON.stringify(this.sampleData),
      tableFields: this.sampleTableFields,
      showActionColumn: true,
      actionColumnHeader: 'Actions',
      actionColumnWidth: 'w-4rem'
    };
  }

  // Configuration for legacy behavior
  get legacyProps(): DynamicComponentProps {
    return {
      id: 'legacy-example',
      value: JSON.stringify(this.sampleData),
      tableFields: this.sampleTableFields
      // No action column configuration - should work as before
    };
  }

  // Action handlers for the custom template
  viewDetails(rowData: any, rowIndex: number): void {
    console.log('View details for row:', rowIndex, rowData);
    // Implement view details logic
  }

  editRow(rowData: any, rowIndex: number): void {
    console.log('Edit row:', rowIndex, rowData);
    // Implement edit logic
  }

  deleteRow(rowData: any, rowIndex: number): void {
    console.log('Delete row:', rowIndex, rowData);
    // Implement delete logic
  }
}
