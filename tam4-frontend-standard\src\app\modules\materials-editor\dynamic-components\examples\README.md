# Dynamic Input Table Component - Action Column Enhancement

## Overview

The `DynamicInputTableComponent` has been enhanced to support configurable action columns while maintaining backward compatibility. This allows for flexible action column implementations without hardcoding specific components.

## New Features

### 1. Configurable Action Column
- **Optional action column**: Can be enabled/disabled via configuration
- **Custom templates**: Support for custom action column content via Angular templates
- **Flexible styling**: Configurable column width and header text
- **Context passing**: Row data and utilities passed to custom templates

### 2. Backward Compatibility
- **Legacy support**: Existing implementations continue to work unchanged
- **Fallback behavior**: Maintains original crossPlantInstances logic when no custom template is provided

## Configuration Options

Add these properties to your `DynamicComponentProps`:

```typescript
interface DynamicComponentProps {
  // ... existing properties
  
  // Action column configuration
  showActionColumn?: boolean;           // Enable/disable action column
  actionColumnTemplate?: TemplateRef<any>; // Custom template for action column content
  actionColumnHeader?: string;          // Header text for action column
  actionColumnWidth?: string;           // CSS width class (default: 'w-3rem')
}
```

## Usage Examples

### Example 1: Custom Action Template

```typescript
@Component({
  template: `
    <dynamic-input-table
      [componentProps]="tableProps"
      [clientId]="clientId"
      page="myPage">
    </dynamic-input-table>

    <!-- Custom action template -->
    <ng-template #actionTemplate let-rowData="rowData" let-rowIndex="rowIndex">
      <div class="flex gap-2">
        <p-button 
          icon="pi pi-eye" 
          size="small" 
          (click)="viewDetails(rowData)">
        </p-button>
        <p-button 
          icon="pi pi-pencil" 
          size="small" 
          (click)="editRow(rowData)">
        </p-button>
      </div>
    </ng-template>
  `
})
export class MyComponent {
  @ViewChild('actionTemplate') actionTemplate!: TemplateRef<any>;

  get tableProps(): DynamicComponentProps {
    return {
      id: 'my-table',
      value: JSON.stringify(this.data),
      tableFields: ['field1', 'field2', 'field3'],
      showActionColumn: true,
      actionColumnTemplate: this.actionTemplate,
      actionColumnHeader: 'Actions',
      actionColumnWidth: 'w-8rem'
    };
  }
}
```

### Example 2: Simple Action Column (No Template)

```typescript
get tableProps(): DynamicComponentProps {
  return {
    id: 'simple-table',
    value: JSON.stringify(this.data),
    tableFields: ['field1', 'field2'],
    showActionColumn: true,
    actionColumnHeader: 'Actions'
  };
}
```

### Example 3: Legacy Behavior (No Changes Required)

```typescript
// Existing code continues to work unchanged
get tableProps(): DynamicComponentProps {
  return {
    id: 'legacy-table',
    value: JSON.stringify(this.data),
    tableFields: ['field1', 'field2']
    // No action column configuration
  };
}
```

## Template Context

When using custom templates, the following context is available:

```typescript
interface ActionColumnContext {
  $implicit: any;              // Row data (same as rowData)
  rowData: any;               // The complete row data object
  rowIndex: number;           // Zero-based row index
  crossPlantInstance: Instance | null; // Legacy crossPlantInstance if available
  onClickHandler: (instance: Instance) => void; // Legacy click handler
}
```

## Migration Guide

### For New Implementations
Use the new configuration options to create flexible action columns:

1. Set `showActionColumn: true` to enable the action column
2. Provide `actionColumnTemplate` for custom content
3. Configure `actionColumnHeader` and `actionColumnWidth` as needed

### For Existing Implementations
No changes required - existing code will continue to work as before. The component automatically detects legacy usage and maintains backward compatibility.

## Best Practices

1. **Template References**: Always use `@ViewChild` with `static: true` for templates used in component properties
2. **Context Usage**: Leverage the provided context to access row data and utilities
3. **Styling**: Use PrimeNG CSS classes for consistent styling (e.g., 'w-3rem', 'w-8rem')
4. **Error Handling**: Always check for null/undefined values in templates
5. **Performance**: Keep template logic simple to maintain table performance

## Implementation Details

The enhancement follows these principles:
- **Non-breaking**: All existing functionality preserved
- **Flexible**: Supports various action column scenarios
- **Consistent**: Follows established Angular and PrimeNG patterns
- **Maintainable**: Clean separation between legacy and new functionality
