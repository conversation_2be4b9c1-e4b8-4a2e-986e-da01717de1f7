import {Component, Input, OnD<PERSON>roy, OnInit} from '@angular/core';
import {Store} from '@ngrx/store';
import {Observable, Subscription} from 'rxjs';
import {InstanceGr, MaterialHistory, MaterialProcess} from '../../model/changelog.model';
import {ProcessDetailsRequest} from '../../store/actions/popup.actions';
import {getLoadingHistory, getMaterialHistory, getMaterialHistoryPage} from '../../store/reducers/index';
import { DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  selector: 'layout-material-history-popup',
  templateUrl: './material-history-popup.component.html',
  styleUrls: ['./material-history-popup.component.scss']
})
export class MaterialHistoryPopupComponent implements OnInit, OnDestroy {

  @Input()
  materialId: string;

  materialHistorySubscribtion: Subscription;
  loadingHistory$: Observable<boolean>;
  activeProcesses: Array<MaterialProcess>;
  completedProcesses: Array<MaterialProcess>;
  abortedProcesses: Array<MaterialProcess>;
  goldenRecordsDeleted: Array<InstanceGr>;
  instancesDetached: Array<InstanceGr>;
  totalNumber: number;

  constructor(private store: Store<any>,  public modalRef: DynamicDialogRef) {
    this.materialHistorySubscribtion = this.store.select(getMaterialHistory).subscribe((data: MaterialHistory) => {
      if (!!data) {
        this.initData(data);
      }
    });
    this.loadingHistory$ = this.store.select(getLoadingHistory);
  }

  ngOnInit() {
  }

  initData(history: MaterialHistory) {
    this.activeProcesses = history.activeProcesses;
    this.completedProcesses = history.completedProcessesList.completedProcesses;
    this.abortedProcesses = history.abortedProcessesList.abortedProcesses;
    this.goldenRecordsDeleted = history.goldenRecordsDeleted;
    this.instancesDetached = history.instancesDetached;
    this.totalNumber = history.completedProcessesList.totalNumber;
  }

  cancel() {
    this.modalRef.close();
    this.modalRef.destroy();
  }

  openProcessDetails(processId: string) {
    this.store.dispatch(new ProcessDetailsRequest({processId: processId}));
  }

  ngOnDestroy() {
    this.materialHistorySubscribtion?.unsubscribe();
  }
}
