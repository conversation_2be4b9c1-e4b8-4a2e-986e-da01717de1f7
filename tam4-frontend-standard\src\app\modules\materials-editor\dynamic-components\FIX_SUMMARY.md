# Fix Summary: Legacy Badge Display Issue

## Problem
After moving the hardcoded logic from `DynamicInputTableComponent` to `DynamicFormInputFactory`, the `app-card-badge` component was not displaying because:

1. The factory was only setting `showActionColumn: true` 
2. No template was provided for rendering the legacy badge
3. The legacy rendering logic was commented out in the component

## Root Cause
The factory cannot create `TemplateRef` objects directly since templates must be defined in components. The original approach of trying to create templates in the factory was architecturally incorrect.

## Solution
Implemented a hybrid approach that maintains the factory-driven configuration while preserving the component's ability to render legacy content:

### 1. Added New Configuration Property
**File**: `dynamic-components.models.ts`
```typescript
interface DynamicComponentProps {
  // ... existing properties
  useLegacyActionColumn?: boolean;  // Enable legacy badge rendering
}
```

### 2. Enhanced Component Logic
**File**: `dynamic-input-table.component.ts`

**New Method**: `shouldShowLegacyBadge(rowIndex: number)`
- Checks if legacy action column is enabled via configuration
- Validates that instances exist for the specific row
- Returns boolean to control legacy badge rendering

**Updated Template Logic**:
```html
<ng-container *ngIf="!componentProps.actionColumnTemplate && shouldShowLegacyBadge(ri)">
  <app-card-badge
    [badgeType]="this.badgeType.PROCESSES"
    [material]="{uuid: this.crossPlantInstances[ri].materialId}"
    *ngIf="this.crossPlantInstances[ri].inProgressProcessCount > 0"
    [value]="this.crossPlantInstances[ri].inProgressProcessCount"
    (click)="onClickHandler(this.crossPlantInstances[ri])"
  ></app-card-badge>
</ng-container>
```

### 3. Updated Factory Configuration
**File**: `dynamic-form-input.factory.ts`

The `crossPlantAggregatedDataBuilder` now sets:
```typescript
out.componentParams.componentProps = {
  ...out.componentParams.componentProps,
  instances: instances,
  showActionColumn: instances.length > 0,
  actionColumnHeader: '',
  actionColumnWidth: 'w-3rem',
  useLegacyActionColumn: true  // ← NEW: Enables legacy badge rendering
};
```

## Key Benefits

### ✅ **Proper Architecture**
- Factory handles configuration logic
- Component handles rendering logic
- Clear separation of concerns maintained

### ✅ **Backward Compatibility**
- Existing `TAM_CrossPlantAggregatedData` functionality restored
- Legacy badge displays correctly
- No breaking changes to existing APIs

### ✅ **Flexibility**
- Custom templates still work when provided
- Legacy behavior can be enabled/disabled via configuration
- Easy to extend for future requirements

### ✅ **Maintainability**
- Logic is centralized in the factory
- Component remains agnostic and configurable
- Clear configuration flags control behavior

## Implementation Flow

1. **Factory Configuration**: When `v.id === 'TAM_CrossPlantAggregatedData'`, the factory:
   - Sets `showActionColumn: true` to display the column
   - Sets `useLegacyActionColumn: true` to enable legacy rendering
   - Provides instances data and column configuration

2. **Component Rendering**: The component:
   - Shows action column when `shouldShowActionColumn()` returns true
   - Renders legacy badge when `shouldShowLegacyBadge(ri)` returns true
   - Falls back to custom template if provided

3. **Legacy Badge Display**: The badge is shown when:
   - No custom template is provided (`!componentProps.actionColumnTemplate`)
   - Legacy action column is enabled (`useLegacyActionColumn: true`)
   - Instances exist for the current row
   - The instance has processes (`inProgressProcessCount > 0`)

## Testing
The fix has been verified to:
- ✅ Display legacy badges for `TAM_CrossPlantAggregatedData`
- ✅ Support custom templates when provided
- ✅ Maintain backward compatibility
- ✅ Follow established architectural patterns

## Future Considerations
This approach provides a solid foundation for:
- Adding more legacy rendering modes
- Supporting different badge types
- Extending configuration options
- Maintaining clean architecture

The solution successfully resolves the display issue while maintaining the architectural improvements achieved in the original refactoring.
