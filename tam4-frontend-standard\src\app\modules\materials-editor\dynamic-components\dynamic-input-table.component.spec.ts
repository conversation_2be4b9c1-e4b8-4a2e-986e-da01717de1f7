import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Component, TemplateRef, ViewChild } from '@angular/core';
import { By } from '@angular/platform-browser';
import { DynamicInputTableComponent } from './dynamic-input-table.component';
import { DynamicComponentProps } from './dynamic-components.models';

// Test host component
@Component({
  template: `
    <dynamic-input-table
      [componentProps]="tableProps"
      [clientId]="'TEST_CLIENT'"
      page="test">
    </dynamic-input-table>

    <ng-template #customActionTemplate let-rowData="rowData" let-rowIndex="rowIndex">
      <button class="test-action-btn" (click)="testAction(rowData, rowIndex)">
        Test Action
      </button>
    </ng-template>
  `
})
class TestHostComponent {
  @ViewChild('customActionTemplate', { static: true }) customActionTemplate!: TemplateRef<any>;

  testData = [
    ['Item 1', 'Description 1'],
    ['Item 2', 'Description 2']
  ];

  tableFields = ['name', 'description'];

  tableProps: DynamicComponentProps = {
    id: 'test-table',
    value: JSON.stringify(this.testData),
    tableFields: this.tableFields
  };

  testAction(rowData: any, rowIndex: number): void {
    console.log('Test action called', rowData, rowIndex);
  }

  // Method to update props for testing
  updateProps(newProps: Partial<DynamicComponentProps>): void {
    this.tableProps = { ...this.tableProps, ...newProps };
  }
}

describe('DynamicInputTableComponent - Action Column Enhancement', () => {
  let component: DynamicInputTableComponent;
  let hostComponent: TestHostComponent;
  let fixture: ComponentFixture<TestHostComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        DynamicInputTableComponent,
        TestHostComponent
      ],
      // Add necessary imports and providers here
    }).compileComponents();

    fixture = TestBed.createComponent(TestHostComponent);
    hostComponent = fixture.componentInstance;
    component = fixture.debugElement.query(By.directive(DynamicInputTableComponent)).componentInstance;
    fixture.detectChanges();
  });

  describe('shouldShowActionColumn', () => {
    it('should return false when no action column configuration is provided', () => {
      expect(component.shouldShowActionColumn()).toBeFalsy();
    });

    it('should return true when showActionColumn is true', () => {
      hostComponent.updateProps({ showActionColumn: true });
      fixture.detectChanges();
      expect(component.shouldShowActionColumn()).toBeTruthy();
    });

    it('should return true when actionColumnTemplate is provided', () => {
      hostComponent.updateProps({ 
        actionColumnTemplate: hostComponent.customActionTemplate 
      });
      fixture.detectChanges();
      expect(component.shouldShowActionColumn()).toBeTruthy();
    });

    it('should return true when crossPlantInstances exist (legacy behavior)', () => {
      component.crossPlantInstances = [{ materialId: 'test', inProgressProcessCount: 1 } as any];
      expect(component.shouldShowActionColumn()).toBeTruthy();
    });
  });

  describe('getActionColumnWidth', () => {
    it('should return default width when no width is specified', () => {
      expect(component.getActionColumnWidth()).toBe('w-3rem');
    });

    it('should return custom width when specified', () => {
      hostComponent.updateProps({ actionColumnWidth: 'w-8rem' });
      fixture.detectChanges();
      expect(component.getActionColumnWidth()).toBe('w-8rem');
    });
  });

  describe('getActionColumnHeader', () => {
    it('should return empty string when no header is specified', () => {
      expect(component.getActionColumnHeader()).toBe('');
    });

    it('should return custom header when specified', () => {
      hostComponent.updateProps({ actionColumnHeader: 'Custom Actions' });
      fixture.detectChanges();
      expect(component.getActionColumnHeader()).toBe('Custom Actions');
    });
  });

  describe('getActionColumnContext', () => {
    it('should create proper context object', () => {
      const testRowData = { name: 'Test Item', description: 'Test Description' };
      const testRowIndex = 1;
      
      const context = component.getActionColumnContext(testRowData, testRowIndex);
      
      expect(context.$implicit).toBe(testRowData);
      expect(context.rowData).toBe(testRowData);
      expect(context.rowIndex).toBe(testRowIndex);
      expect(context.crossPlantInstance).toBeNull();
      expect(typeof context.onClickHandler).toBe('function');
    });

    it('should include crossPlantInstance when available', () => {
      const testInstance = { materialId: 'test', inProgressProcessCount: 1 } as any;
      component.crossPlantInstances = [testInstance];
      
      const context = component.getActionColumnContext({}, 0);
      
      expect(context.crossPlantInstance).toBe(testInstance);
    });
  });

  describe('Template Integration', () => {
    it('should render custom action template when provided', () => {
      hostComponent.updateProps({
        showActionColumn: true,
        actionColumnTemplate: hostComponent.customActionTemplate
      });
      fixture.detectChanges();

      const actionButtons = fixture.debugElement.queryAll(By.css('.test-action-btn'));
      expect(actionButtons.length).toBe(hostComponent.testData.length);
    });

    it('should show action column header when configured', () => {
      hostComponent.updateProps({
        showActionColumn: true,
        actionColumnHeader: 'Test Actions'
      });
      fixture.detectChanges();

      const headerCells = fixture.debugElement.queryAll(By.css('th'));
      const actionHeader = headerCells.find(cell => 
        cell.nativeElement.textContent.trim() === 'Test Actions'
      );
      expect(actionHeader).toBeTruthy();
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain legacy behavior when no action column config is provided', () => {
      // This test ensures existing functionality is not broken
      expect(component.shouldShowActionColumn()).toBeFalsy();
      expect(component.getActionColumnWidth()).toBe('w-3rem');
      expect(component.getActionColumnHeader()).toBe('');
    });

    it('should show legacy crossPlantInstances action when available', () => {
      component.crossPlantInstances = [{ materialId: 'test', inProgressProcessCount: 1 } as any];
      
      expect(component.shouldShowActionColumn()).toBeTruthy();
    });
  });
});
