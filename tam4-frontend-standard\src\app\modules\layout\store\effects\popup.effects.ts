/* tslint:disable:max-line-length */
import {PlatformLocation} from '@angular/common';
import {HttpClient, HttpErrorResponse} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {GlobalError, GlobalSuccess} from '@creactives/tam4-common-ui';
import {Actions, createEffect, ofType} from '@ngrx/effects';
import {Store} from '@ngrx/store';
import {TranslateService} from '@ngx-translate/core';
import {BsModalService} from 'ngx-bootstrap/modal';
import {EMPTY, of} from 'rxjs';
import {
    catchError,
    concatMap,
    exhaustMap,
    flatMap,
    map,
    mergeMap,
    switchMap,
    tap,
    withLatestFrom
} from 'rxjs/operators';
import {environment} from 'src/environments/environment';
import {MaterialHistory, TaskNotesHistoryResponse} from '../../model/changelog.model';
import {IMaterialPlantUpdate, IPlantExtensionDetails} from '../../model/plant.model';
import {AssignmentPopupComponent} from '../../popups/assignment-popup/assignment-popup.component';
import {MaterialHistoryPopupComponent} from '../../popups/material-history-popup/material-history-popup.component';
import {PlantExtensionPopupComponent} from '../../popups/plant-extension-popup/plant-extension-popup.component';
import {PlantUpdatePopupComponent} from '../../popups/plant-update-popup/plant-update-popup.component';
import {ProcessDetailsPopupComponent} from '../../popups/process-details-popup/process-details-popup.component';
import {
    ClientStatuses
} from '../../popups/relationship-popup/wizard/masterdata-list-view-only/masterdata-list-view-only.component';
import {AssignmentService} from '../../services/assignment.service';
import {AuditPopupService} from '../../services/audit-popup.service';
import {MaterialService, PlantValidationErrors} from '../../services/material.service';
import {PlantsService} from '../../services/plants.service';
import {
    AddPlantLangToCache,
    AssignMaterialResponse,
    AssignmentFailure,
    AssignmentRequest,
    AssignmentSuccessful,
    CheckForAdditionalEdits,
    CloseAllPopups,
    ClosePlantExtensionPopup,
    ClosePlantUpdatePopup,
    DeletePlantExtensionSuccessful,
    EditPlantExtensionSuccessful,
    GetAvailableClientsFailure,
    GetAvailableClientsRequest,
    GetAvailableClientsSuccessful,
    GetExtensionEditorRequest,
    GetMaterialPlants,
    GetMaterialPlantsFailure,
    GetMaterialPlantsSuccessful,
    GetOriginalMaterialFailure,
    GetOriginalMaterialPlantUpdateFailure,
    GetOriginalMaterialPlantUpdateRequest,
    GetOriginalMaterialPlantUpdateSuccessful,
    GetOriginalMaterialRequest,
    GetOriginalMaterialSuccessful,
    GetPlantConfigurationsFailure,
    GetPlantConfigurationsRequest,
    GetPlantConfigurationsSuccessful,
    GetPlantLangExtendFailure,
    GetPlantLangExtendRequest,
    GetPlantLangExtendSuccess,
    GetPlantLanguagesFailure,
    GetPlantLanguagesRequest,
    GetPlantLanguagesSuccess,
    GetPlantsFailure,
    GetPlantsSuccessful,
    GetPlantsUpdateFailure,
    GetPlantsUpdateSuccessful,
    GetPlantUpdateEditorRequest,
    IPlantExtensionDetail,
    LoadStatusByClientsFailure,
    LoadStatusByClientsRequest,
    LoadStatusByClientsSuccessful,
    MaterialPlantData,
    MaterialsAssigned,
  OpenHistoryInProgressPopup,
    OpenHistoryPopup,
    OpenNotesHistoryPopup,
    OpenPlantExtensionPopup,
    OpenPlantUpdatePopup,
    PlantApprovalConfigFailure,
    PlantApprovalConfigMaterialDetailLoadRequest,
    PlantApprovalConfigMaterialDetailLoadSuccess,
    PlantApprovalConfigRequest,
    PlantApprovalConfigSuccess,
    PlantExtensionDescError,
    PlantExtensionDescErrorEnd,
    PlantExtensionFailure,
    PlantExtensionHandlingAddPlantView,
    PlantExtensionRequest,
    PlantExtensionSuccessful,
    PlantLanguageResponse,
    PlantUpdateConfigPlantFailure,
    PlantUpdateConfigPlantRequest,
    PlantUpdateConfigPlantSuccess,
    PlantUpdateFailure,
    PlantUpdateRequest,
    PlantUpdateSuccessful,
    PopupActionTypes,
    ProcessDetailsFailure,
    ProcessDetailsRequest,
    ProcessDetailsSuccess,
    ProcessMaterialAssigned,
    RequestMaterialHistory,
    RequestMaterialHistoryFailure,
  RequestMaterialHistoryInProgress,
    RequestMaterialHistorySuccess,
    RequestTaskNotesHistory,
    RequestTaskNotesHistorySuccess,
    UpdatePlantApprovalAfterSetValueFailure,
    UpdatePlantApprovalAfterSetValueRequest,
    UpdatePlantApprovalAfterSetValueSuccess,
    UpdatePlantExtensionAfterSetValueFailure,
    UpdatePlantExtensionAfterSetValueRequest,
    UpdatePlantExtensionAfterSetValueSuccess,
    UpdatePlantExtensionUpdateAfterSetValueFailure,
    UpdatePlantExtensionUpdateAfterSetValueRequest,
    UpdatePlantExtensionUpdateAfterSetValueSuccess,
    UpdatePlantLangExtend,
} from '../actions/popup.actions';
import {getUserType} from '../profile/profile.state';
import {
    getAssignmentProcessedMaterials,
    getCurrentLanguage,
    getFallbackLanguages,
    getIsAddPlantView,
    getPlantExtensionClient,
    getPlantExtensionDetailPlantCodes,
    getRelationshipCreationSelectedMaterials,
    getSelectedPlantExtension
} from '../reducers';
import {ObjectsUtils, ReduxUtils} from '../../../../utils';
import {FormState} from 'src/app/modules/components/item-details/form.state';
import {ValidateUpdatedMaterialResponse} from '../../pages/md-editor/store/md-editor.service';
import {PlantRenamedFields, TamApePageType} from 'src/app/models';
import {DialogService, DynamicDialogConfig} from "primeng/dynamicdialog";
import {defaultModalConfig, defaultModalStyle, defaultModalStyleBig, defaultModalStyleSmall} from 'src/app/utils/common.constants';
import {WorkflowCommentsService} from "../../overlays/workflow-comments.service";

function mapWithLabel(clientStatuses: ClientStatuses) {
    const clientStatusesWithLabel: ClientStatuses = {};
    Object.keys(clientStatuses).forEach((client: string) => {
            const clientStatus = clientStatuses[client];
            clientStatusesWithLabel[client] = clientStatus.map(a => ({
                ...a,
                label: a.key + ' - ' + a.text
            }));
        }
    );
    return clientStatusesWithLabel;
}


// noinspection JSUnusedGlobalSymbols
@Injectable()
export class PopupEffects {

    constructor(
        private actions$: Actions,
        private store: Store<any>,
        private modalService: BsModalService,
        private assignmentService: AssignmentService,
        private materialService: MaterialService,
        private auditService: AuditPopupService,
        private plantsService: PlantsService,
        public httpClient: HttpClient,
        private translate: TranslateService,
        private location: PlatformLocation,
        private dialogService: DialogService,
        private workflowCommentService: WorkflowCommentsService
    ) {
        this.location.onPopState(() => {
            this.store.dispatch(new CloseAllPopups());
        });
    }

    ref: any;

    openAssignmentPopup$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.OPEN_ASSIGNMENT_POPUP),
        tap((action: any) => {
            this.modalService.show(AssignmentPopupComponent, {
                initialState: action.payload,
                class: 'modal-lg'
            });
        })
    ), {dispatch: false});
    dispatchAssignment$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.ASSIGNMENT_REQUEST),
        withLatestFrom(this.store.select(getAssignmentProcessedMaterials)),
        mergeMap((action: [AssignmentRequest, AssignMaterialResponse]) =>
            this.assignmentService.assign(action[0].payload.groups).pipe(
                mergeMap((results: AssignMaterialResponse) => {
                    const goForAdditionalEdits =
                        results.materialsNotAssigned &&
                        results.materialsNotAssigned.length > 0 &&
                        !action[0].payload.groups.force;
                    if (goForAdditionalEdits) {
                        return of(new CheckForAdditionalEdits(results));
                    } else {
                        const combineProcessedMaterials: Array<ProcessMaterialAssigned> =
                            [];

                        const combineResults: Array<MaterialsAssigned> = [];
                        if (action[1]) {
                            combineResults.push(...action[1].materialsAssigned);
                        }
                        combineResults.push(...results.materialsAssigned);

                        combineProcessedMaterials.push(
                            ...combineResults.map((res) => {
                                return {
                                    processId: res.processId,
                                    materialIds: [res.materialId]
                                } as ProcessMaterialAssigned;
                            })
                        );
                        return of(
                            new AssignmentSuccessful(combineProcessedMaterials),
                            new GlobalSuccess(this.translate.instant('layout.worklists.enrichment.assignment.assign-sucess'))
                        );
                    }
                }),
                catchError((err: HttpErrorResponse) =>
                    of(new AssignmentFailure(err), new GlobalError(err?.message))
                )
            )
        )
    ));
    closeAssignmentPopup$ = createEffect(() => this.actions$.pipe(
        ofType(
            PopupActionTypes.ASSIGNMENT_CANCELLED,
            PopupActionTypes.ASSIGNMENT_SUCCESSFUL
        ),
        tap(() => {
            this.modalService.hide();
        })
    ), {dispatch: false});
    openPlantExtensionPopup$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.OPEN_PLANT_EXTENSION_POPUP),
        tap((action: OpenPlantExtensionPopup) => {
            this.modalService.show(PlantExtensionPopupComponent, {
                initialState: {materialId: action.payload.materialId},
                class: 'w-85vw-fixed h-95vh-fixed',
                ignoreBackdropClick: true
            });
        })
    ), {dispatch: false});
    destroyPlantExtensionPopup$ = createEffect(() => this.actions$.pipe(
        ofType(
            PopupActionTypes.PLANT_EXTENSION_CANCELLED,
            PopupActionTypes.PLANT_EXTENSION_SUCCESSFUL,
            PopupActionTypes.GET_ORIGINAL_MATERIAL_FAILURE
        ),
        map(() => new ClosePlantExtensionPopup())
    ));
    callBackendForGetOriginalMaterial$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.GET_ORIGINAL_MATERIAL_REQUEST),
        withLatestFrom(
            this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(getPlantExtensionClient),
        ),
        switchMap((action: [GetOriginalMaterialRequest, string, string[], string]) => {
            return this.materialService.getPlantExtensionDataWithDefaultValues(action[0].payload.materialId, action[1], action[2], action[3])
                .pipe(
                    map(
                        (result) =>
                            new GetOriginalMaterialSuccessful(
                                {
                                    result
                                },
                                action[0].payload.materialId
                            )
                    ),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            new GetOriginalMaterialFailure(err),
                            new GlobalError(err?.error?.message)
                        )
                    )
                );
        })
    ));
    callBackendForGetAvailableClientsExtension$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.GET_AVAILABLE_CLIENTS_REQUEST),
        switchMap((action: GetAvailableClientsRequest) => {
            return this.materialService.getAvailableClientForExtension(action.payload.materialId)
                .pipe(
                    map((result) =>
                        new GetAvailableClientsSuccessful(result)
                    ),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            new GetAvailableClientsFailure(err),
                            new GlobalError(err?.error?.message)
                        )
                    )
                );
        })
    ));
    validateUpdatedMasterdataExtension$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.UPDATE_PLANT_EXTENSION_AFTER_SET_VALUE_REQUEST),
        withLatestFrom(
            this.store.select(getCurrentLanguage),
            this.store.select(getUserType),
            this.store.select(getFallbackLanguages),
        ),
        concatMap((it: [UpdatePlantExtensionAfterSetValueRequest, string, string, Array<string>]) => {

            const updateEditorPayload: GetExtensionEditorRequest = {
                value: it[0].payload.newValue,
                page: TamApePageType.EXTENSION.toString(),
                language: it[1],
                attributeName: it[0].payload.attributeName,
                materialDetails: null,
                plantDetails: [it[0].payload.plantExtensionDetail],
                client: it[0].payload.plantExtensionDetail.client,
                fallbackLanguages: it[3],
                descriptionLanguage: null
            };
            return this.materialService.updatePlantEditorAfterSetValue(updateEditorPayload, it[0].payload.materialId).pipe(
                concatMap((response) => {

                    const responseFormState = response.formState as ValidateUpdatedMaterialResponse;
                    return [
                        new UpdatePlantExtensionAfterSetValueSuccess({
                                ...response,
                                formState: FormState.buildFromBackend(responseFormState)
                            }
                        )
                    ];
                }),
                catchError((err: HttpErrorResponse) =>
                    of(
                        new UpdatePlantExtensionAfterSetValueFailure(
                            (err.error && err.error.message) || err.message
                        ),
                        new GlobalError(err?.error?.message)
                    )
                )
            );
        })
    ));

    validateUpdatedMasterdataUpdate$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.UPDATE_PLANT_EXTENSION_UPDATE_AFTER_SET_VALUE_REQUEST),
        withLatestFrom(
            this.store.select(getCurrentLanguage),
            this.store.select(getUserType),
            this.store.select(getFallbackLanguages),
        ),
        concatMap((it: [UpdatePlantExtensionUpdateAfterSetValueRequest, string, string, Array<string>]) => {
            const updateEditorPayload: GetPlantUpdateEditorRequest = {
                value: it[0].payload.newValue,
                page: TamApePageType.EXTENSION_MODIFICATION.toString(),
                language: it[1],
                attributeName: PlantRenamedFields._switchBackAttributeName(it[0].payload.attributeName),
                materialDetails: null,
                plantExtensionDetailChanges: [it[0].payload.plantUpdateDetail],
                client: it[0].payload.plantUpdateDetail.client,
                fallbackLanguages: it[3],
                descriptionLanguage: null
            };
            return this.materialService.updatePlantUpdateEditorAfterSetValue(updateEditorPayload, it[0].payload.materialId).pipe(
                concatMap((response) => {

                    const responseFormState = response.formState as ValidateUpdatedMaterialResponse;

                    return [
                        new UpdatePlantExtensionUpdateAfterSetValueSuccess({
                                ...response,
                                formState: FormState.buildFromBackend(responseFormState)
                            }
                        )
                    ];
                }),
                catchError((err: HttpErrorResponse) =>
                    of(
                        new UpdatePlantExtensionUpdateAfterSetValueFailure(
                            (err.error && err.error.message) || err.message
                        ),
                        new GlobalError(err?.error?.message)
                    )
                )
            );
        })
    ));
    validateUpdatedMasterdataApproval$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.UPDATE_PLANT_APPROVAL_AFTER_SET_VALUE_REQUEST),
        withLatestFrom(
            this.store.select(getCurrentLanguage),
            this.store.select(getUserType),
            this.store.select(getFallbackLanguages),
        ),
        concatMap((it: [UpdatePlantApprovalAfterSetValueRequest, string, string, Array<string>]) => {
            const updateEditorPayload: GetPlantUpdateEditorRequest = {
                value: it[0].payload.newValue,
                page: TamApePageType.APPROVAL_EXTENSION.toString(),
                language: it[1],
                attributeName: it[0].payload.attributeName,
                materialDetails: null,
                plantExtensionDetailChanges: [it[0].payload.plantUpdateDetail],
                client: it[0].payload.plantUpdateDetail.client,
                fallbackLanguages: it[3],
                descriptionLanguage: null
            };
            return this.materialService.updatePlantUpdateEditorAfterSetValue(updateEditorPayload, it[0].payload.materialId).pipe(
                concatMap((response) => {

                    const responseFormState = response.formState as ValidateUpdatedMaterialResponse;

                    return [
                        new UpdatePlantApprovalAfterSetValueSuccess({
                                ...response,
                                formState: FormState.buildFromBackend(responseFormState)
                            }
                        )
                    ];
                }),
                catchError((err: HttpErrorResponse) =>
                    of(
                        new UpdatePlantApprovalAfterSetValueFailure(
                            (err.error && err.error.message) || err.message
                        ),
                        new GlobalError(err?.error?.message)
                    )
                )
            );
        })
    ));
    onAddPlantExtension$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.PLANT_EXTENSION_POPUP_HANDLING_ADD_PLANT_VIEW),
        withLatestFrom(
            this.store.select(getPlantExtensionDetailPlantCodes),
            this.store.select(getIsAddPlantView)
        ),
        mergeMap((action: [PlantExtensionHandlingAddPlantView, string[], boolean]) => {
            const isAddPlantView = action[2];

            if (!isAddPlantView && action[0].payload.plantExtensionDetail !== null) {
                return [
                    new GetPlantLangExtendRequest({
                        client: action[0].payload.plantExtensionDetail.client,
                        codes: action[1]
                    })
                ];
            }
            return EMPTY;
        })
    ));
    onDeletePlantExtensionSuccess$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.DELETE_PLANT_EXTENSION_SUCCESSFUL),
        withLatestFrom(
            this.store.select(getPlantExtensionDetailPlantCodes),
            this.store.select(getIsAddPlantView)
        ),
        mergeMap((action: [DeletePlantExtensionSuccessful, string[], boolean]) => {

            const isAddPlantView = action[2];
            if (!isAddPlantView && action[1].length > 0) {
                return [
                    new GetPlantLangExtendRequest({
                        client: action[0].payload.plantExtensionDetail.client,
                        codes: action[1]
                    })
                ];
            }

            return EMPTY;

        })
    ));
    onEditPlantExtensionSuccessfuly$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.EDIT_PLANT_EXTENSION_SUCCESSFUL),
        withLatestFrom(
            this.store.select(getPlantExtensionDetailPlantCodes),
            this.store.select(getIsAddPlantView)
        ),
        mergeMap((action: [EditPlantExtensionSuccessful, string[], boolean]) => {

            const isAddPlantView = action[2];
            if (!isAddPlantView && action[1].length > 0) {
                return [
                    new GetPlantLangExtendRequest({
                        client: action[0].payload.plantExtensionDetail.client,
                        codes: action[1]
                    })
                ];
            }

            return EMPTY;

        })
    ));
    onUpdateLanguagePlantsExtend$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.UPDATE_PLANT_LANG_EXTEND),
        withLatestFrom(
            this.store.select(getPlantExtensionDetailPlantCodes),
            this.store.select(getIsAddPlantView),
            this.store.select(getSelectedPlantExtension)
        ),
        mergeMap((action: [UpdatePlantLangExtend, string[], boolean, IPlantExtensionDetail]) => {
            const isAddPlantView = action[2];
            if (!isAddPlantView) {
                return [
                    new GetPlantLangExtendRequest({
                        client: action[0].payload.client,
                        codes: action[1].filter(lang => lang !== action[3].plantCode)
                    })
                ];
            }

            return EMPTY;

        })
    ));

    // @Effect()
    // onPlantExtensionChange$ = this.actions$.pipe(
    //   ofType(PopupActionTypes.PLANT_EXTENSION_CHANGE),
    //   mergeMap((action: PlantExtensionChange) => {
    //     return [new UpdatePlantLangExtend({client: action.payload.plant.client, language: action.payload.plant.code})]
    //   })
    // )
    openPlantUpdatePopup$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.OPEN_PLANT_UPDATE_POPUP),
        tap((action: OpenPlantUpdatePopup) => {
            this.modalService.show(PlantUpdatePopupComponent, {
                initialState: {materialId: action.payload.materialId},
                class: 'w-85vw-fixed h-95vh-fixed',
                ignoreBackdropClick: true
            });

        })
    ), {dispatch: false});
    closePlantUpdatePopup = createEffect(() => this.actions$.pipe(
        ofType(
            PopupActionTypes.CLOSE_PLANT_UPDATE_POPUP,
            PopupActionTypes.CLOSE_PLANT_EXTENSION_POPUP,
            PopupActionTypes.CLOSE_POPUPS,
            PopupActionTypes.CLOSE_NOTES_HISTORY_POPUP
        ),
        tap(() => {
            this.modalService.hide();
            if (this.ref) {
                this.dialogService.getInstance(this.ref)?.close();
                this.ref?.close();
                this.ref?.destroy();
            }
        })
    ), {dispatch: false});

    closePlantExtensionPopup = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.CLOSE_PLANT_EXTENSION_POPUP),
        map(() => {
            // clearing cache after closing extension popup
            return new AddPlantLangToCache([]);
        })
    ));
    destroyPlantUpdatePopup$ = createEffect(() => this.actions$.pipe(
        ofType(
            PopupActionTypes.PLANT_UPDATE_CANCELLED,
            PopupActionTypes.PLANT_UPDATE_SUCCESSFUL
        ),
        map(() => new ClosePlantUpdatePopup())
    ));
    callBackendForGetOriginalMaterialUpdate$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.GET_ORIGINAL_MATERIAL_PLANT_UPDATE_REQUEST),
        tap((action: GetOriginalMaterialPlantUpdateRequest) =>
            this.store.dispatch(
                new GetMaterialPlants({materialId: action.payload.materialId})
            )
        ),
        withLatestFrom(
            this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages)
        ),
        switchMap((action: [GetOriginalMaterialPlantUpdateRequest, string, string[]]) =>
            this.materialService
                .getPlantEditConfigurationData(action[0].payload.materialId, action[1], action[2])
                .pipe(
                    mergeMap(
                        (results: MaterialPlantData) =>
                            [
                                new GetOriginalMaterialPlantUpdateSuccessful({result: results}),
                                new GetPlantLanguagesRequest({
                                    client: results.client,
                                    codes: results.plantCode
                                })
                            ]
                    ),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            new GetOriginalMaterialPlantUpdateFailure(err),
                            new GlobalError(err?.error?.message)
                        )
                    )
                )
        )
    ));
    getPlantLanguages$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.GET_PLANT_LANGUAGES_REQUEST),
        exhaustMap((action: GetPlantLanguagesRequest) =>
            this.materialService.getPlantLanguages(action.payload)
                .pipe(
                    mergeMap((result: PlantLanguageResponse) => [new GetPlantLanguagesSuccess(result)]
                    ),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            new GetPlantLanguagesFailure(err),
                            new GlobalError(err?.error?.message)
                        )
                    )
                )
        )
    ));
    getPlantLangExtend$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.GET_PLANT_LANG_EXTEND_REQUEST),
        mergeMap((action: GetPlantLangExtendRequest) =>
            this.materialService.getPlantLanguages(action.payload)
                .pipe(
                    mergeMap((result: PlantLanguageResponse) => [new GetPlantLangExtendSuccess(result)]
                    ),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            new GetPlantLangExtendFailure(err),
                            new GlobalError(err?.error?.message)
                        )
                    )
                )
        )
    ));
    callBackendForGetMaterialPlants$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.GET_MATERIAL_PLANTS),
        switchMap((action: GetMaterialPlants) =>
            this.materialService.getPlantEditData(action.payload.materialId).pipe(
                map((response: IMaterialPlantUpdate) => response.plantDetails),
                map(
                    (results: IPlantExtensionDetails[]) =>
                        new GetMaterialPlantsSuccessful({result: results})
                ),
                catchError((err: HttpErrorResponse) =>
                    of(
                        new GetMaterialPlantsFailure(err),
                        new GlobalError(err?.error?.message)
                    )
                )
            )
        )
    ));
    // getPlantsUpdateSuccessful$ = createEffect(() => this.actions$.pipe(
    //     ofType(
    //         PopupActionTypes.GET_PLANTS_UPDATE_SUCCESSFUL,
    //         PopupActionTypes.PLANT_UPDATE_CHANGE
    //     ),
    //     switchMap(() => [
    //         new GetMaterialStatusValuesUpdateRequest(),
    //         new GetPurchasingGroupValuesUpdateRequest()
    //     ])
    // ));
    callBackendForPlants$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.GET_PLANTS_REQUEST),
        switchMap(() =>
            this.materialService.getPlantValues().pipe(
                map(
                    (results: any) => new GetPlantsSuccessful({result: results.plants})
                ),
                catchError((err: HttpErrorResponse) =>
                    of(new GetPlantsFailure(err), new GlobalError(err?.message))
                )
            )
        )
    ));
    callBackendForPlantsUpdate$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.GET_PLANTS_UPDATE_REQUEST),
        switchMap(() =>
            this.materialService.getPlantValues().pipe(
                map(
                    (results: any) =>
                        new GetPlantsUpdateSuccessful({result: results.plants})
                ),
                catchError((err: HttpErrorResponse) =>
                    of(new GetPlantsUpdateFailure(err), new GlobalError(err?.message))
                )
            )
        )
    ));


// -----------------------------  EXTENSION --------------------------------------


    dispatchPlantUpdateRequest$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.PLANT_UPDATE_REQUEST),
        flatMap((action: PlantUpdateRequest) =>
            this.plantsService.requestPlantUpdateWithChanges(action.payload).pipe(
                flatMap((response) => {
                    if (response?.hasErrors) {
                        const errMessage = this.getErrorPlantUpdateRequest(response);
                        return of(new PlantUpdateFailure(new HttpErrorResponse({error: errMessage}), ObjectsUtils.forceCast<PlantValidationErrors>(response)), new GlobalError(errMessage));
                    }


                    return of(
                        new PlantUpdateSuccessful(),
                        new GlobalSuccess(this.translate.instant('layout.plant-update-popup.plant-update-successful'))
                    );

                }),
                catchError((err: HttpErrorResponse) =>
                    of(new PlantUpdateFailure(err), new GlobalError(err?.error?.message))
                )
            )
        )
    ));

    dispatchPlantExtensionRequest$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.PLANT_EXTENSION_REQUEST),
        flatMap((action: PlantExtensionRequest) =>
            this.materialService
                .materialPlantExtension(action.payload.materialPlantExtension)
                .pipe(
                    flatMap((response) => {
                            if (response.hasErrors) {
                                const errMess: string = response.errorMessage;
                                const errorDesc: Array<{
                                    field: string,
                                    language: string
                                }> = response.fieldsWithErrors;
                                return of(new PlantExtensionDescError({
                                        error: errMess,
                                        tasksLoading: [],
                                        errorDesc
                                    }),
                                    new PlantExtensionDescErrorEnd({error: '', errorDesc: undefined}));
                            } else {
                                return of(
                                    new PlantExtensionSuccessful(),
                                    new GlobalSuccess(this.translate.instant('layout.plant-extension-popup.plant-extension-successful'))
                                );
                            }
                        }
                    ),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            new PlantExtensionFailure(err),
                            new GlobalError(err?.error?.message)
                        )
                    )
                )
        )
    ));
    openMaterialHistoryPopup$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.OPEN_HISTORY_POPUP),
        map((action: OpenHistoryPopup) => {
            return new RequestMaterialHistory({
                request: {
                    page: 1,
                    itemsPerPage: environment.pageSize,
                    materialId: action.payload.materialId
                }
            });
        })
    ));
    fetchMaterialHistoryEffect$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.REQUEST_MATERIAL_HISTORY),
        mergeMap((action: RequestMaterialHistory) => {
            return this.auditService.getMaterialHistory(action.payload.request).pipe(
                map((r: MaterialHistory) => new RequestMaterialHistorySuccess({
                    materialId: action.payload.request.materialId,
                    history: r
                })),
                catchError((err: HttpErrorResponse) =>
                    of(
                        new RequestMaterialHistoryFailure({error: err?.message}),
                        new GlobalError(err?.error?.message)
                    ))
            );
        }))
    );
    openMaterialHistoryInProgressPopup$ = createEffect(() => this.actions$.pipe(
    ofType(PopupActionTypes.OPEN_HISTORY_IN_PROGRESS_POPUP),
    map((action: OpenHistoryInProgressPopup) => {
      return new RequestMaterialHistoryInProgress({
        request: {
          page: 1,
          itemsPerPage: environment.pageSize,
          materialId: action.payload.materialId
        }
      });
    })
  ));
  fetchMaterialHistoryInProgressEffect$ = createEffect(() => this.actions$.pipe(
    ofType(PopupActionTypes.REQUEST_MATERIAL_HISTORY_IN_PROGRESS),
    mergeMap((action: RequestMaterialHistoryInProgress) => {
      return this.auditService.getMaterialHistoryInProgress(action.payload.request).pipe(
        map((r: MaterialHistory) => new RequestMaterialHistorySuccess({
          materialId: action.payload.request.materialId,
          history: r
        })),
        catchError((err: HttpErrorResponse) =>
          of(
            new RequestMaterialHistoryFailure({error: err?.message}),
            new GlobalError(err?.error?.message)
          ))
      );
    }))
  );
  openMaterialHistoryPopupEffect$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.REQUEST_MATERIAL_HISTORY_SUCCESS),
        tap((action: RequestMaterialHistorySuccess) => {
            const modalCfg: DynamicDialogConfig = {
                ...defaultModalConfig,
                closable: false,
                showHeader: false,
                data: {
                    materialId: action.payload.materialId
                }
            };
            this.ref = this.dialogService.open(MaterialHistoryPopupComponent, modalCfg);
        })), ReduxUtils.noDispatch()
    );
    ProcessDetailsRequest$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.PROCESS_DETAILS_REQUEST),
        map((action: ProcessDetailsRequest) => action.payload.processId),
        mergeMap((processId) =>
            this.auditService.getMaterialHistoryDetail(processId).pipe(
                map((processDetails) => new ProcessDetailsSuccess({processDetails})),
                catchError((error: HttpErrorResponse) => {
                        const errorMessage = error.message;
                        return of(new ProcessDetailsFailure({error: errorMessage}),
                            new GlobalError(errorMessage));
                    }
                )
            )
        )
    ));
    ProcessDetailsSuccess$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.PROCESS_DETAILS_SUCCESS),
        tap((_) => {
            // this.modalService.show(ProcessDetailsPopupComponent, {
            //     class: 'modal-dialog modal-dialog-centered modal-lg',
            //     ignoreBackdropClick: true,
            //     backdrop: 'static',
            //
            // })

            const modalCfg: DynamicDialogConfig = {
                ...defaultModalStyleBig,
                closeOnEscape: false,
                closable: false,
                resizable: false,
                showHeader: false,
            };
            this.ref = this.dialogService.open(ProcessDetailsPopupComponent, modalCfg);
        })
    ), {dispatch: false});

    dispatchLoadStatusByClientsRequest$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.LOAD_STATUS_BY_CLIENTS),
        withLatestFrom(
            this.store.select(getRelationshipCreationSelectedMaterials),
            this.store.select(getCurrentLanguage)
        ),
        map(([, materials, language]) => {
            const clientSet = materials.reduce(
                (acc, item) =>
                    acc.indexOf(item.client) >= 0 ? acc : [...acc, item.client],
                []
            );
            return new LoadStatusByClientsRequest(clientSet, language);
        })
    ));
    doLoadStatusByClientsRequest$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.LOAD_STATUS_BY_CLIENTS_REQUEST),
        withLatestFrom(this.store.select(getFallbackLanguages)),
        mergeMap(
            ([action, fallbackLanguages]: [LoadStatusByClientsRequest, string[]]) =>
                this.materialService
                    .getMaterialStatusValuesByClient(
                        action.clients,
                        action.language,
                        fallbackLanguages
                    )
                    .pipe(
                        map((it) => new LoadStatusByClientsSuccessful(mapWithLabel(it))),
                        catchError((error: HttpErrorResponse) => {
                                const errorMessage = error.message;
                                return of(new LoadStatusByClientsFailure({error: errorMessage}),
                                    new GlobalError(errorMessage));
                            }
                        )
                    )
        )));


    // APPROVAL

    OnGetPlantConfigurations$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.GET_PLANT_CONFIGURATION_REQUEST),
        withLatestFrom(
            this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages)
        ),
        tap(([action, currLang, fallbackLang]: [GetPlantConfigurationsRequest, string, string[]]) => {
            this.store.dispatch(new PlantApprovalConfigMaterialDetailLoadRequest(action?.masterId));
        }),
        switchMap((params: [GetPlantConfigurationsRequest, string, string[]]) =>
            this.materialService
                .getPlantConfigurations(
                    params[0].page,
                    params[1],
                    params[2],
                    params[0].masterId,
                    params[0].plantCode
                )
                .pipe(
                    map(
                        (results: any) =>
                            new GetPlantConfigurationsSuccessful(results)
                    ),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            new GetPlantConfigurationsFailure(err),
                            new GlobalError(err?.message)
                        )
                    )
                )
        )
    ));


    openNotesHistoryPopup$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.OPEN_NOTES_HISTORY_POPUP),
        map((action: OpenNotesHistoryPopup) => {
            return new RequestTaskNotesHistory({
                request: {
                    taskId: action.payload?.taskId,
                    processId: action.payload?.processId
                }
            });
        })
    ));
    fetchTaskNotesHistoryEffect$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.REQUEST_NOTES_HISTORY),
        tap((action: RequestTaskNotesHistory) => {

            if (action?.payload?.request?.taskId) {
                return this.assignmentService.getTaskHistory(action.payload.request).subscribe({
                        next: (r: TaskNotesHistoryResponse) => {
                            this.store.dispatch(new RequestTaskNotesHistorySuccess({
                                taskId: action.payload.request.taskId,
                                history: r,
                                showSidebar: false
                            }));
                        },
                        error: (err) => {
                            this.store.dispatch(new RequestMaterialHistoryFailure({error: err?.message}));
                            this.store.dispatch(new GlobalError(err?.error?.message));
                        }
                    }
                );
            } else if (action?.payload?.request?.processId) {
                this.store.dispatch(new RequestTaskNotesHistorySuccess({
                    showSidebar: true
                }));
            } else {
                return this.store.dispatch(new GlobalError("worklist.history.no-process-selected"));
            }
        })), ReduxUtils.noDispatch()
    );

    onGetPlantUpdateConfiguration$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.PLANT_UPDATE_CONFIG_PLANT_REQUEST),
        withLatestFrom(
            this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages)
        ),
        switchMap(([action, currLang, fallbackLang]: [PlantUpdateConfigPlantRequest, string, string[]]) =>
            this.materialService
                .getPlantConfigurations(
                    action.page,
                    currLang,
                    fallbackLang,
                    action.masterId,
                    action.plantCode
                )
                .pipe(
                    map(
                        (results: any) =>
                            new PlantUpdateConfigPlantSuccess(results)
                    ),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            new PlantUpdateConfigPlantFailure(err),
                            new GlobalError(err?.message)
                        )
                    )
                )
        )
    ));

    onPlantExtensionApprovalMaterialDetailRequest$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.PLANT_APPROVAL_CONFIG_MATERIAL_DETAIL_LOAD_REQUEST),
        tap((action: PlantApprovalConfigMaterialDetailLoadRequest) =>
            this.materialService.getItemDetails(action.masterdataId).subscribe({
                next: (materialSnapshot) => {
                    this.store.dispatch(new PlantApprovalConfigMaterialDetailLoadSuccess(materialSnapshot?.materialDetails));
                }
            })
        )
    ), ReduxUtils.noDispatch());


    onGetPlantApprovalConfiguration$ = createEffect(() => this.actions$.pipe(
        ofType(PopupActionTypes.PLANT_APPROVAL_CONFIG_REQUEST),
        withLatestFrom(
            this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages)
        ),
        tap(([action, currLang, fallbackLang]: [PlantApprovalConfigRequest, string, string[]]) => {
            this.store.dispatch(new PlantApprovalConfigMaterialDetailLoadRequest(action?.payload?.masterId));
        }),
        switchMap(([action, currLang, fallbackLang]: [PlantApprovalConfigRequest, string, string[]]) =>
            this.materialService
                .getPlantApprovalConfiguration(
                    action.payload,
                    currLang,
                    fallbackLang
                )
                .pipe(
                    map((results: any) => new PlantApprovalConfigSuccess(results)),
                    catchError((err: HttpErrorResponse) =>
                        of(
                            new PlantApprovalConfigFailure(err),
                            new GlobalError(err?.message)
                        )
                    )
                )
        )
    ));

    getErrorPlantUpdateRequest(response: PlantValidationErrors) {
        const myErrorMessages: string[] = [];

        response?.formStatesList?.forEach(fs => {
            let currSubMsg = `${fs?.plantKey?.client}/${fs?.plantKey?.code}: `;
            currSubMsg = currSubMsg + fs?.formState?.fieldStatus?.map(f => f.statusMessage).join(',');
            myErrorMessages.push(currSubMsg);
        });

        return myErrorMessages.join('\n');
    }


}
