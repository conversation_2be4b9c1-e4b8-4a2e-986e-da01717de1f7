import { AfterContentInit, ApplicationRef, ChangeDetectorRef, Component, ContentChildren, Do<PERSON>heck, EventEmitter, input, Input, OnDestroy, OnInit, Output, QueryList, Signal, ViewChild } from '@angular/core';

import { FormGroup } from '@angular/forms';
import { AttachmentContentInfo, AttachmentInfo, SelectorMap } from '@creactives/models';
import { Tam4TranslationService } from '@creactives/tam4-translation-core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { PrimeTemplate } from "primeng/api";
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { takeUntil } from 'rxjs/operators';
import { Tam4SelectorConfig, TamAbstractReduxComponent } from 'src/app/components';
import { Tam4ComponentEvent, TamApePageType } from "src/app/models";
import { DynamicFormEventPayload } from "src/app/modules/materials-modal/store/materials-modal.action-types";
import { EventUtils, ObjectsUtils } from 'src/app/utils';
import { CommonUtils } from "src/app/utils/common.utils";
import { selectEnableDevDiagnostic } from '../../layout/store/profile/profile.state';
import { MaterialsModalSelectors } from '../../materials-modal/store/materials-modal.selectors';
import { MaterialsModalState } from '../../materials-modal/store/materials-modal.state';
import { SmartCreationLinkUnlinkConfirm } from '../../smart-creation/components/smart-creation-link-unlink-confirm';
import { SmartCreationSearchLinkInstancesDialog } from '../../smart-creation/components/smart-creation-search-link-instances-modal.dialog';
import { SmartCreationFormControl } from '../../smart-creation/models/smart-creation-form.types';
import { Instance, SuggestGrAttributes } from '../../smart-creation/models/smart-creation-validation.types';
import { GoldenRecordInstance, LinkUnlinkInstancesRequest, SmartCreationMaterialDetail, SmartFieldConfiguration } from '../../smart-creation/models/smart-creation.types';
import { DynamicTableGrInstanceComponent } from '../dynamic-components/dynamic-table-gr-instance.component';
import { BaseMaterialEditorTabsKeys, SelectedCategoriesNotSuggested, SmartItemTab, ViewModeEnum } from '../models/material-editor.types';

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'unlinkInstancesDone', selector: MaterialsModalSelectors.getUnlinkInstancesDone},
  {key: 'instancesWithRelationships', selector: MaterialsModalSelectors.getInstancesWithRelationships},
];

@Component({
  selector: 'div[scMaterialDetails]',
  host: {
    "[class]": "'flex flex-column gap-3'"
  },
  template: `
    @if (appendTop) {
      <ng-container *ngTemplateOutlet="appendTop?.template"></ng-container>
    }
    <p-panel class="compat" id="attachments" *ngIf="showAttachments">
      <ng-template pTemplate="header">
        <div class="accordion-header flex gap-3">
          <span class="tab-tile vertical-align-middle font-semibold">
            {{ 'smartCreation.attachments.label' | translate }}
          </span>
        </div>
      </ng-template>
      <material-attachments [disabled]="disabled"
                            [attachments]="attachments"
                            [acceptedAttachmentsMimeTypes]="acceptedAttachmentsMimeTypes()"
                            (attachmentUpload)="handleAttachmentUpload($event)"
                            (attachmentRemove)="handleAttachmentRemove($event)"
                            (attachmentDownload)="handleAttachmentDownload($event)"
      ></material-attachments>
    </p-panel>

    @if (categoriesFormGroup()) {
      <p-panel class="compat" id="classification">
        <ng-template pTemplate="header">
          <div class="accordion-header flex gap-3">
        <span class="tab-tile vertical-align-middle font-semibold">
        {{ 'smartCreation.classification.label' | translate }}
        </span>
          </div>
        </ng-template>
        <categories-selection
            [categoriesFormGroup]="categoriesFormGroup()"
            [selectedCategoriesNotSuggested]="selectedCategoriesNotSuggested"
            [disabled]="!changeClassificationEnabled"
            [viewMode]="viewMode"
            [disableReset]="true"
            (resetClicked)="handleCategoriesResetClicked($event)"
            (selectManuallyClicked)="handleSelectManuallyClicked($event)"
            (selectedCategoryChanged)="handleSelectedCategoryChanged($event)"
        ></categories-selection>

      </p-panel>
    }
    @if (dynamicFormGroup()) {
      @for (formData of dynamicFormGroup(); track formData; let i = $index) {
        <p-panel class="compat" [formGroup]="formData?.formGroup" [id]="formData.key">
          <ng-template pTemplate="header">
            <div materialDetailsAccordionHeader [tab]="formData"></div>
          </ng-template>
          @switch (formData.key) {
            @case (BaseMaterialEditorTabsKeys.GOLDEN_RECORD) {
              <div scMaterialGoldenRecord [goldenRecordSheet]="goldenRecordSheet"
                   [page]="page"
                   (selectPlantClicked)="handleSelectPlantClicked($event)"
                   [viewMode]="viewMode"
                   [notEnabled]="!(viewMode === ViewModeEnum.CREATE)"
                   (goldenRecordDetailsChanged)="onGoldenRecordChanged($event)">
              </div>
            }
            @case (BaseMaterialEditorTabsKeys.DESCRIPTIONS) {
              <div scMaterialDetailsDescriptionEditor
                   [viewMode]="viewMode"
                   [page]="page"
                   [viewMode]="viewMode"
                   [descriptionFormData]="formData"></div>
            }
            @case (BaseMaterialEditorTabsKeys.PLANT_DATA) {
              <div scMaterialPlantData [plantSheet]="plantSheet"
                   [dynamicFormData]="formData"
                   [showFieldWithNoValue]="showFieldWithNoValue"
                   [page]="page"
                   [locale]="currentLocale()"
                   [plants]="plants"
                   [initialData]="initialData()"
                   [viewMode]="viewMode"
                   [singlePlantAddOnly]="true"
                   [currentClient]="currentClient"
                   (inputChange)="inputChange.emit($event)"
                   (onUpdatePlantData)="handleUpdatePlantData($event)">
              </div>
            }

             @case (BaseMaterialEditorTabsKeys.THUMBNAILS) {
                  <!-- @if(viewMode === ViewModeEnum.GR_APPROVE) { -->
                    <div scMaterialImageCarousel
                      [thumbnailInstances] = "imageInstances"
                      (onSelectThumbnails)="handleSelectedImage($event)">
                    </div>
                  <!-- } -->
              }

            @default {
              @for (inputComponent of formData?.items; track inputComponent) {
                <span scDynamicContainerTemplate
                      [showEmptyValues]="showFieldWithNoValue"
                      [dynamicComponent]="inputComponent?.component"
                      [dynamicParams]="getParams(formData?.formGroup, inputComponent?.componentParams, i)"
                      [label]="inputComponent?.label | translate"
                      [mandatory]="inputComponent?.mandatory"
                      [coreAttribute]="inputComponent?.componentParams?.coreAttribute"
                      [editable]="inputComponent?.componentParams?.editable"
                      [viewMode]="viewMode"
                      [locale]="currentLocale()"
                      [suggestions]="getSuggestAttributes(inputComponent?.id)"
                      (formComponentEvent)="handleDynamicEvent($event)"
                      [currentClient]="currentClient"
                      [enableDevDiagnostic]="enableDevDiagnostic"
                ></span>
              }

            }
          }
        </p-panel>
      }
    }
    @if (initialData()?.additionalMaterialInformation?.relationshipsDetails?.length > 0) {
      <p-panel id="relations" class="compat">
        <ng-template pTemplate="header">
          <div class="accordion-header flex gap-3">
            <span class="tab-tile vertical-align-middle font-semibold">{{ 'layout.item-details.tabs.relations' | translate }}</span>
          </div>
        </ng-template>
        <relations-table
            [relations]="initialData()?.additionalMaterialInformation?.relationshipsDetails"
            [canDeleteRelationship]="initialData()?.materialActionsAuthorization?.canDeleteRelationship?.enabled"
            (relationshipDeletion)="handleRelationshipDeletion($event)">
        </relations-table>
      </p-panel>
    }
    @if (signals?.instancesWithRelationships()?.length > 0) {
      <p-panel id="instances" class="compat">
        <ng-template pTemplate="header">
          <div class="accordion-header flex gap-3">
            <span class="tab-tile vertical-align-middle font-semibold">{{ 'layout.item-details.tabs.instances' | translate }}</span>
          </div>
        </ng-template>
        <instances-table [instances]="signals.instancesWithRelationships()"
                         [canUnlink]="viewMode === ViewModeEnum.GR_EDIT && initialData()?.materialActionsAuthorization?.canLinkUnlinkInstances?.enabled"
                         (onSelectItem)="selectItem($event)" (onUnselectItem)="unselectItem($event)"
                         (unlinkInstance)="handleUnlinkInstances($event)">
        </instances-table>
        <ng-template pTemplate="footer"
                     *ngIf="viewMode === ViewModeEnum.GR_EDIT && initialData()?.materialActionsAuthorization?.canLinkUnlinkInstances?.enabled">
          <div class="flex align-items-center gap-3">
            <p-button styleClass="p-button-primary p-button-fixed" (click)="handleLinkInstances($event)"
                      [disabled]="(selectedItems && selectedItems.length > 0)">
              <i class="fas fa-link fa-sm"></i> {{ 'smartCreation.modalDetails.linkUnlink.link-instances' | translate }}
            </p-button>
            <p-button styleClass="p-button-primary p-button-fixed" (click)="handleUnlinkInstances($event)"
                      [disabled]="(selectedItems && selectedItems.length === 0)">
              <i class="fas fa-unlink fa-sm"></i> {{ 'smartCreation.modalDetails.linkUnlink.unlink-instances' | translate }}
            </p-button>
          </div>
        </ng-template>
      </p-panel>
    }
    @if (appendBottom) {
      <ng-container *ngTemplateOutlet="appendBottom.template"></ng-container>
    }
  `,
  // changeDetection: ChangeDetectionStrategy.OnPush

})
export class MaterialDetailsComponent extends TamAbstractReduxComponent<SelectorMap> implements OnInit, OnDestroy, AfterContentInit, DoCheck {

  readonly TamApePageType = TamApePageType;
  @ViewChild(DynamicTableGrInstanceComponent) dynamicTableGrInstanceComponent: DynamicTableGrInstanceComponent;

  @Input({required: true}) page: string;
  dynamicFormGroup?: Signal<any[]> = input.required<any[]>();
  @Input() plantSheet?: SmartItemTab;
  @Input() plants?: any;
  @Input() goldenRecordSheet?: SmartItemTab;
  @Input() attachments: AttachmentInfo[];
  initialData?: Signal<SmartCreationMaterialDetail> = input<SmartCreationMaterialDetail>(null);

  @Input() showGrFromScratch: boolean = false;

  categoriesFormGroup: Signal<FormGroup> = input.required<FormGroup>();
  @Input() selectedCategoriesNotSuggested: SelectedCategoriesNotSuggested;
  @Input() disabled: boolean;
  @Input() viewMode: ViewModeEnum;
  @Input() suggestedAttributes: SuggestGrAttributes;
  @Input() showFieldWithNoValue: boolean = true;

  @Input() changeClassificationEnabled: boolean = false;

  @Input() showAttachments: boolean = true;

  acceptedAttachmentsMimeTypes = input<string[]>(null);

  @Output() resetClicked = new EventEmitter<string>();
  @Output() selectManuallyClicked = new EventEmitter<string>();
  @Output() selectedCategoryChanged = new EventEmitter<string>();
  @Output() updatePlantData = new EventEmitter<Tam4ComponentEvent<string, any>>();

  @Output() inputChange = new EventEmitter<SmartFieldConfiguration>();
  @Output() categoryResetClicked = new EventEmitter<string>();
  @Output() categorySelectManuallyClicked = new EventEmitter<string>();
  @Output() categorySelectedChanged = new EventEmitter<string>();
  @Output() onUpdateAlternativeUom = new EventEmitter<Tam4ComponentEvent<any, DynamicFormEventPayload>>();
  @Output() attachmentUpload = new EventEmitter<File>();
  @Output() attachmentRemove = new EventEmitter<string>();
  @Output() attachmentDownload = new EventEmitter<string>();
  @Output() onSelectThumbnails = new EventEmitter<string>();
  @Output() plantSelectManuallyClicked = new EventEmitter<string>();
  @Output() goldenRecordChanged = new EventEmitter<any>();
  @Output() relationshipDeletion = new EventEmitter<{ relationshipId: string; successfulOperation: boolean }>();
  @Output() onGrInstancePlantEvent = new EventEmitter<Tam4ComponentEvent<any, {
    instance: GoldenRecordInstance,
    view: ViewModeEnum,
    page: string
  }>>();


  @Input() currentClient: string;
  @Input() materialId: string;

  @Input() imageInstances: Array<AttachmentContentInfo>;

  activeIndex: number[] = [];
  plantsGrDialogRef: DynamicDialogRef | undefined;

  @Input() link = true;
  @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate>;
  appendTop: PrimeTemplate;
  appendBottom: PrimeTemplate;

  enableDevDiagnostic: boolean = false;
  selectedItems: Array<Instance> = [];
  protected readonly ViewModeEnum = ViewModeEnum;
  protected readonly BaseMaterialEditorTabsKeys = BaseMaterialEditorTabsKeys;

  constructor(private cdRef: ChangeDetectorRef, private appRef: ApplicationRef, private dialogService: DialogService, protected store: Store<MaterialsModalState>,
              protected tamTranslate: Tam4TranslationService, protected translate: TranslateService) {
    super(translate, tamTranslate, store, storeSelectors);

  }

  ngAfterContentInit(): void {
    this.templates.forEach(template => {
      switch (template.name) {
        case 'appendTop':
          this.appendTop = template;
          break;
        case 'appendBottom':
          this.appendBottom = template;
          break;
        default:
          // Trattamento del template di default (content)
          break;
      }
    });
  }

  ngDoCheck(): void {
    // CommonUtils.pingForCd(this.cdRef, this.appRef);
  }

  ngOnInit() {


    CommonUtils.pingForCd(this.cdRef);

    this.store.select(selectEnableDevDiagnostic)
      .pipe(takeUntil(this.ngDestroy$))
      .subscribe(enabled => {
        this.enableDevDiagnostic = enabled || false;
      });
  }

  ngOnDestroy() {
    super.ngOnDestroy();
    this.selMap?.unlinkInstancesDone.subscribe(value => {
      if (value) {
        this.dynamicTableGrInstanceComponent.resetTable();
      }
    });
  }

  onInputValueEvent(id: string, formGroup: FormGroup, sheetIndex: number, suggestedValue?: string) {
    if (formGroup?.controls?.[id] !== null) {
      const itemIndex = this.dynamicFormGroup()[sheetIndex].items.findIndex(x => x.id === id);
      if (itemIndex >= 0) {
        const {
          component,
          componentParams,
          ...currentControl
        } = this.dynamicFormGroup()[sheetIndex].items[itemIndex];

        const currValue = formGroup?.controls?.[id]?.value;
        currentControl.value = ObjectsUtils.isNotNoU(currValue) ? ObjectsUtils.flatArray(currValue) : suggestedValue;

        if (this.dynamicFormGroup()[sheetIndex].items[itemIndex].unitsOfMeasure &&
          this.dynamicFormGroup()[sheetIndex].items[itemIndex].unitsOfMeasure.length > 0) {
          currentControl.unitsOfMeasureSelected = formGroup?.controls[id + '.mu']?.value?.toString();
        }

        // this.dynamicFormGroup()[sheetIndex].errors = [];
        this.inputChange.emit(currentControl);
        if (this.activeIndex.indexOf(sheetIndex + 1) === -1) {
          this.activeIndex.push(sheetIndex + 1);
        }
      }
    }
  }

  onChangeUnitOfMeasureEvent(id: string, selectedUnitOfMeasure: string, formGroup: FormGroup, sheetIndex: number) {

    const itemIndex = this.dynamicFormGroup()[sheetIndex].items.findIndex(x => x.id === id);
    if (itemIndex >= 0) {
      const currentControl = this.dynamicFormGroup()[sheetIndex].items[itemIndex];
      currentControl.value = formGroup.controls[id].value?.toString();
      currentControl.unitsOfMeasureSelected = selectedUnitOfMeasure;

      const currentUomControl: SmartFieldConfiguration = {
        id: id + '.mu',
        attributeName: id + '.mu',
        value: selectedUnitOfMeasure
      };

      this.inputChange.emit(currentUomControl);

      if (this.activeIndex.indexOf(sheetIndex + 1) === -1) {
        this.activeIndex.push(sheetIndex + 1);
      }
    }
  }

  getParams(formGroup: FormGroup | undefined, params: any, sheetIndex: number) {
    if (!params) {
      params = {};
    }

    const _dynamicFormGroup = this.dynamicFormGroup()[sheetIndex];

    if (params?.formControlName === '4_TAM_AlternativeUnitOfMeasure') {
      params.componentProps.standardUom = _dynamicFormGroup.formGroup.controls['4_TAM_UnitOfMeasure'].value;
    }

    params.viewMode = this.viewMode;

    if (ObjectsUtils.isNoU(formGroup)) {
      return {
        ...params
      };
    }

    const fieldErrors = _dynamicFormGroup?.errors?.filter(e => e.field === params.formControlName);
    (formGroup?.controls?.[params.formControlName] as SmartCreationFormControl).validateErrors = fieldErrors;

    return {
      hasError: fieldErrors?.length > 0,
      sheetIndex,
      errors: fieldErrors,
      hasWarning: false,
      onInputValueEvent: (id: string, formGroupInp: FormGroup, sheetIndexInp: number, suggestedValue?: string) =>
        this.onInputValueEvent(id, formGroupInp, sheetIndexInp, suggestedValue),
      onChangeUnitOfMeasureEvent: (id: string,
                                   selectedUnitOfMeasure: string,
                                   formGroupInp: FormGroup,
                                   sheetIndexInp: number) =>
        this.onChangeUnitOfMeasureEvent(id, selectedUnitOfMeasure, formGroupInp, sheetIndexInp),
      ...params
    };
  }


  // TODO: Da eliminare appena il recupero di attachmentInfo per thumbnails è disponibile
    imageMartello = "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"; 
    imageChiodo = "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";
    imageScreen1 = "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";
    imageScreen2 = "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";
    thumbnailInstances: AttachmentContentInfo[] = [
    {
      attachmentId: "123e4567-e89b-12d3-a456-426614174000",
      fileName: "martello",
      size: this.imageMartello.length,
      date: new Date(),
      content: this.base64ToUint8Array(this.imageMartello),
      client: "100",
      materialCode: "334455655"
    },
    {
      attachmentId: "123e4567-e89b-12d3-a456-426614174001",
      fileName: "chiodo",
      size: this.imageChiodo.length,
      date: new Date(),
      content: this.base64ToUint8Array(this.imageChiodo),
      client: "200",
      materialCode: "334455675"
    },
    {
      attachmentId: "123e4567-e89b-12d3-a456-426614174002",
      fileName: "screen1",
      size: this.imageScreen1.length,
      date: new Date(),
      content: this.base64ToUint8Array(this.imageScreen1),
      client: "200",
      materialCode: "334455685"
    },
    {
      attachmentId: "123e4567-e89b-12d3-a456-426614174003",
      fileName: "screen2",
      size: this.imageScreen2.length,
      date: new Date(),
      content: this.base64ToUint8Array(this.imageScreen2),
      client: "100",
      materialCode: "334455657"
    }];

    base64ToUint8Array(base64: string): Uint8Array {
      const binaryString = atob(base64);
      const len = binaryString.length;
      const bytes = new Uint8Array(len);
      for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      return bytes;
    }

  getSuggestAttributes(id: string) {
    return id && this.suggestedAttributes ? this.suggestedAttributes[id] : [];
  }

  // hasMandatoryFields(tabKey: string): boolean {
  //   const form = this.dynamicFormGroup()?.filter(f => ObjectsUtils.isNotNoU(f)).find(form => tabKey === form?.key);
  //   if (form) {
  //     return form.items.some(item => item.mandatory);
  //   }
  //   return false;
  // }

  open(e) {
    if (this.activeIndex.indexOf(e.index) === -1) {
      this.activeIndex.push(e.index);
    }
  }

  close(e) {
    const i = this.activeIndex.indexOf(e.index);
    if (i >= 0) {
      this.activeIndex.splice(i, 1);
    }
  }

  handleUpdatePlantData(attributes: Tam4ComponentEvent<string, any>) {

    this.updatePlantData.emit(attributes);
  }

  handleCategoriesResetClicked(taxonomy: string) {

  }

  handleSelectManuallyClicked(taxonomy: string) {
    this.categorySelectManuallyClicked.emit(taxonomy);
  }

  handleSelectedCategoryChanged(taxonomy: string) {
    this.categorySelectedChanged.emit(taxonomy);
  }

  handleAttachmentUpload(file: File) {
    this.attachmentUpload.emit(file);
  }

  handleAttachmentRemove(uuid: string) {
    this.attachmentRemove.emit(uuid);
  }

  handleAttachmentDownload(uuid: string) {
    this.attachmentDownload.emit(uuid);
  }

  handleDynamicEvent(event$: Tam4ComponentEvent<any, any>): void {
    switch (event$?.type) {
      case 'onUpdateAlternativeUomFn':
        this.onUpdateAlternativeUom.emit(event$);
        break;
      default:
        break;
    }
  }

  handleRelationshipDeletion(event: { relationshipId: string; successfulOperation: boolean }) {
    this.relationshipDeletion.emit(event);
  }

  onGoldenRecordChanged(event: any) {
    this.goldenRecordChanged.emit(event);
  }

  handleSelectPlantClicked(event: GoldenRecordInstance) {

    this.onGrInstancePlantEvent.emit({
      type: 'OPEN-GR-INSTANCE-PLANT-SELECTION',
      payload: {instance: event, view: this.viewMode, page: this.page}
    });
  }

  selectItem(selectedMaterial: Instance) {
    this.selectedItems.push(selectedMaterial);
  }

  unselectItem(data: Instance) {
    this.selectedItems = this.selectedItems.filter(item => item.materialId !== data.materialId);
  }

  handleLinkInstances(event$: any) {
    EventUtils.stopPropagation(event$);
    const smartCreationMaterialDetail = this.initialData();
    const goldenRecordCode: string = smartCreationMaterialDetail.additionalMaterialInformation.goldenRecordCode;
    const instancesClient: string[] = smartCreationMaterialDetail.additionalMaterialInformation.instances.map(i => i.materialKey.client);
    const goldenRecordMaterialId: string = smartCreationMaterialDetail.additionalMaterialInformation.materialId;
    const description: string = smartCreationMaterialDetail.additionalMaterialInformation.description;
    const modalCfg: DynamicDialogConfig = {
      header: this.translate.instant('smartCreation.modalDetails.linkUnlink.search-instances'),
      data: {goldenRecordCode, materialId: goldenRecordMaterialId, instancesClient, description},
    };

    this.dialogService.open(SmartCreationSearchLinkInstancesDialog, modalCfg);
  }

  handleUnlinkInstances(event$: any) {
    EventUtils.stopPropagation(event$);
    const goldenRecordCode: string = this.initialData().additionalMaterialInformation.goldenRecordCode;
    const goldenRecordMaterialId: string = this.initialData().additionalMaterialInformation.materialId;
    const unlinkInstances = this.selectedItems.map(e => ({
      materialId: e.materialId,
      clientId: e.materialKey.client,
      goldenRecord: false,
      goldenRecordCode: e.materialKey.materialCode,
      description: e.description,
      materialCode: e.materialKey.materialCode
    }));
    const request: LinkUnlinkInstancesRequest = {
      goldenRecordCode,
      goldenRecord: goldenRecordMaterialId,
      unlinkInstances
    };
    const modalCfg: DynamicDialogConfig = {
      header: this.translate.instant('smartCreation.modalDetails.linkUnlink.unlink-instances'),
      data: {request},
    };

    this.plantsGrDialogRef = this.dialogService.open(SmartCreationLinkUnlinkConfirm, modalCfg);
  }

  handleSelectedImage(thumbnailsId: string) {
        this.onSelectThumbnails.emit(thumbnailsId);
        console.log('Immagine selezionata:', thumbnailsId);
  }

}
