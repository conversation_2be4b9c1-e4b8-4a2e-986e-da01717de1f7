import {Observable} from "rxjs";
import {FormGroup} from "@angular/forms";
import {Relationship, ViewModeEnum} from "../models/material-editor.types";
import {CategoryKey, FieldType, MaterialPlantDetails} from "@creactives/models";
import {
    AlternativeUnitsOfMeasure,
    Instance,
    LocalizedFieldValue,
    SuggestAttribute
} from "../../smart-creation/models/smart-creation-validation.types";
import {DynamicBaseInputComponent} from "./dynamic-baseinput.component";
import {DynamicDescriptionComponent} from "./dynamic-description.component";
import {EventEmitter, TemplateRef} from "@angular/core";
import {NumericAttributeValue, SimpleAttributeValue, Tam4ComponentEvent} from "src/app/models";
import {DynamicFormEventPayload} from "src/app/modules/materials-modal/store/materials-modal.action-types";
import {SmartFieldConfiguration} from "../../smart-creation/models/smart-creation.types";

export type SmartCreationFieldFetchSuggestionFn = (source: string,
                                                   id: string,
                                                   query: string,
                                                   documentData: any,
                                                   clientId?: string,
                                                   page?: string) => Observable<any>;

export interface DocumentData {
  [key: string]: string | SimpleAttributeValue | NumericAttributeValue;
}

export interface GroupedKeyValue {
  label?: string,
  value: any,
  items: any[]
}

export const ATTRIBUTEVALUETYPE = {
  DESCRIPTION: 'description',
  NORMALIZEDESCRIPTION: 'normalizeDescription'
};


export interface IChangeAttribute {
  changes: {
    [key: string]: DescriptionsChange | ChangeAttribute<MaterialPlantDetails>[] | ChangeAttribute<CategoryKey>
      | ChangeAttribute<AlternativeUnitsOfMeasure>[] | ChangeAttribute<TechnicalAttribute>[]
      | ChangeAttribute<string> | ChangeAttribute<number>
  };
}

export interface ChangeAttribute<T> {
  oldValue: T;
  newValue: T;
}

export interface DescriptionsChange {
  [key: string]: ChangeAttribute<string>;
}

export interface TechnicalAttribute {
  '@type': string;
  code?: string;
  value?: string;
  tamFieldCode?: string;
  unitOfMeasure?: string;
}

export interface DynamicComponentProps {
  id?: string,
  value?: any;
  relatedAttribute?: string;
  dropdownListSource?: string;
  loadItems?: (source: string, id: string, documentData: DocumentData, clientId?: string, page?: string) => Observable<any>;
  suggestions?: any[];
  length?: number;
  backendFilter?: boolean;
  fetchSuggestionsFn?: (source: string, id: string, query: string, documentData: DocumentData, clientId?: string, page?: string) => Observable<any>;
  items?: LocalizedFieldValue[];
  multiple?: boolean;
  relationships?: Relationship[];
  goldenRecord?: string;
  goldenRecordCode?: string;
  unitsOfMeasure?: string[];
  unitsOfMeasureSelected?: string;
  tableFields?: string[];
  type?: string;
  details?: any[];
  editable?: boolean;
  decimals?: number;
  actionType?: 'clipboard' | 'input';
  suggestedValue?: string;
  suggestionsAttribute?: SuggestAttribute[];
  onDescriptionInputChange?: (source: string, value: string) => void;
  formGroup?: any;
  mandatory?: boolean;
  textarea?: boolean;
  hasError?: boolean;
  hasWarnings?: boolean;
  prefix?: string;
  viewMode?: ViewModeEnum;
  standardUom?: string;
  uomDropdownValues?: LocalizedFieldValue[];
  textareaSizeRows?: number;
  textareaSizeCols?: number;
  instances?: Instance[];
  sheetIndex?: number;
  tabIndex?: number;
  children?: SmartFieldConfiguration[];
  // Action column configuration
  showActionColumn?: boolean;
  actionColumnTemplate?: TemplateRef<any>;
  actionColumnHeader?: string;
  actionColumnWidth?: string;
}

export interface DynamicInputComponentProps {
  id?: string;
  page: string;
  sheetIndex?: number;
  tabIndex?: number;
  formGroup?: FormGroup;
  formControlName?: string;
  hasError?: boolean;
  hasWarning?: boolean;
  errors?: any[];
  warnings?: any[];
  componentProps?: DynamicComponentProps;
  defaultValue?: any;
  mandatory?: boolean;
  coreAttribute?: boolean;
  editable?: boolean;
  customerFieldInvalidForClientConfig?: boolean;
  viewMode?: ViewModeEnum | undefined;
  type?: FieldType;
  useTranslatePipe?: string;
  goldenRecordAttribute?: boolean;
  children?: any[];
  formComponentEvent?: EventEmitter<Tam4ComponentEvent<any, DynamicFormEventPayload>>;
  onInputValueEvent?: (id: string, formGroup?: FormGroup, sheetIndex?: number) => void;
  onChangeUnitOfMeasureEvent?: (id: string,
                                selectedUnitOfMeasure: string,
                                formGroup?: FormGroup,
                                sheetIndex?: number) => void;
}


export interface DynamicComponentWrapper {
  component: typeof DynamicBaseInputComponent | typeof DynamicDescriptionComponent;
  componentParams: DynamicInputComponentProps;
}
