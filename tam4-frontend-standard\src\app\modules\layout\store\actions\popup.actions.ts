/* tslint:disable:max-line-length */
import {HttpErrorResponse} from '@angular/common/http';
import {Action} from '@ngrx/store';
import {EditPlantUpdateRequestPayload, IPlantExtensionDetails, IPlantUpdateDetail, PlantUpdateDetail, PlantUpdateRequestPayload} from '../../model/plant.model';
import {
  GetProcessInfo,
  GetProcessInfoFailure,
  GetProcessInfoRelationshipSuccessful,
  GetProcessInfoSuccessful,
  OpenExtensionApprovalPopup,
  OpenRelationshipApprovalPopup,
  OpenUpdateApprovalPopup,
  RejectFailure,
  ResetApprovalEdit
} from './popup-extended.actions';
import {MaterialHistory, MaterialHistoryProcessesRequest, TabName, TaskNotesHistoryRequest, TaskNotesHistoryResponse} from '../../model/changelog.model';
import {ClientStatuses} from '../../popups/relationship-popup/wizard/masterdata-list-view-only/masterdata-list-view-only.component';
import {<PERSON><PERSON>ey, ItemTab, MaterialDetails, ProcessDetails, SimplifiedMaterial} from '@creactives/models';
import {ItemDetailsAttributes} from '../../model/popup.model';
import {ValidateUpdatedMaterialResponse} from '../../pages/md-editor/store/md-editor.service';
import {FormState} from 'src/app/modules/components/item-details/form.state';
import {PlantValidationErrors} from 'src/app/modules/layout/services/material.service';

export enum PopupActionTypes {
  APPROVAL_CANCELLED = 'popups/plant-approval/cancelled',
  APPROVAL_FAILURE = 'popups/plant-approval/failure',
  APPROVAL_REQUEST = 'popups/plant-approval/request',
  APPROVAL_SUCCESSFUL = 'popups/plant-approval/successful',
  ASSIGNMENT_CANCELLED = 'popups/assignment/cancelled',
  ASSIGNMENT_FAILURE = 'popups/assignment/failure',
  ASSIGNMENT_REQUEST = 'popups/assignment/request',
  ASSIGNMENT_SUCCESSFUL = 'popups/assignment/successful',
  CLOSE_PLANT_EXTENSION_POPUP = 'popups/plant-extension/close',
  CLOSE_PLANT_UPDATE_POPUP = 'popups/plant-edit/close',
  DELETE_PLANT_EXTENSION_REQUEST = 'popups/delete-plant-extension/request',
  DELETE_PLANT_EXTENSION_SUCCESSFUL = 'popups/delete-plant-extension/successful',
  DELETE_PLANT_UPDATE_SUCCESSFUL = 'popups/delete-plant-update/successful',
  DELETE_PLANT_UPDATE_REQUEST = 'popups/delete-plant-update/request',
  EDIT_PLANT_EXTENSION_REQUEST = 'popups/edit-plant-extension/request',
  EDIT_PLANT_EXTENSION_SUCCESSFUL = 'popups/edit-plant-extension/successful',
  EDIT_PLANT_UPDATE_REQUEST = 'popups/edit-plant-update/request',
  EDIT_PLANT_UPDATE_SUCCESSFUL = 'popups/edit-plant-update/successful',

  GET_MATERIAL_PLANTS = 'get-material-plants/request',
  GET_MATERIAL_PLANTS_FAILURE = 'get-material-plants/failure',
  GET_MATERIAL_PLANTS_SUCCESSFUL = 'get-material-plants/successful',


  // ---------------------------------------------------------------------------------------------
  GET_ORIGINAL_MATERIAL_FAILURE = 'get-original-material/failure',
  GET_ORIGINAL_MATERIAL_PLANT_UPDATE_FAILURE = 'get-original-material-update/failure',
  GET_ORIGINAL_MATERIAL_PLANT_UPDATE_REQUEST = 'get-original-material-update/request',
  GET_ORIGINAL_MATERIAL_PLANT_UPDATE_SUCCESSFUL = 'get-original-material-update/successful',

  GET_ORIGINAL_MATERIAL_REQUEST = 'get-original-material/request',
  GET_ORIGINAL_MATERIAL_SUCCESSFUL = 'get-original-material/successful',
  GET_AVAILABLE_CLIENTS_REQUEST = 'get-available-clients/request',
  GET_AVAILABLE_CLIENTS_SUCCESSFUL = 'get-available-clients/successful',
  GET_AVAILABLE_CLIENTS_FAILURE = 'get-available-clients/failure',
  GET_PLANTS_FAILURE = 'get-plants/failure',
  GET_PLANTS_REQUEST = 'get-plants/request',
  GET_PLANTS_SUCCESSFUL = 'get-plants/successful',
  GET_PLANTS_UPDATE_FAILURE = 'get-plants-update/failure',
  GET_PLANTS_UPDATE_REQUEST = 'get-plants-update/request',
  GET_PLANTS_UPDATE_SUCCESSFUL = 'get-plants-update/successful',
  GET_PROCESS_INFO = 'popups/get-process-info/request',
  GET_PROCESS_INFO_FAILURE = 'popups/get-process-info/failure',
  GET_PROCESS_INFO_RELATIONSHIP_SUCCESSFUL = 'popups/get-process-info-relationship/successful',
  GET_PROCESS_INFO_SUCCESSFUL = 'popups/get-process-info/successful',


  OPEN_ASSIGNMENT_POPUP = 'popups/assignment/open',
  OPEN_EXTENSION_APPROVAL_POPUP = 'popups/extension-approval/open',
  OPEN_PLANT_EXTENSION_POPUP = 'popups/plant-extension/open',
  OPEN_PLANT_UPDATE_POPUP = 'popups/plant-edit/open',
  OPEN_RELATIONSHIP_APPROVAL_POPUP = 'popups/relationship-approval/open',
  OPEN_RELATIONSHIP_CREATE_POPUP = 'popups/relationship/open',
  OPEN_UPDATE_APPROVAL_POPUP = 'popups/plant-update-approval/open',
  PLANT_EXTENSION_CANCELLED = 'popups/plant-extension/cancelled',
  PLANT_EXTENSION_CHANGE = 'popups/plant-extension/change',
  PLANT_EXTENSION_FAILURE = 'popups/plant-extension/failure',
  PLANT_EXTENSION_POPUP_HANDLING_ADD_PLANT_VIEW = 'popups/plant-extension/handling-add-plant-view',
  PLANT_EXTENSION_REQUEST = 'popups/plant-extension/request',
  PLANT_EXTENSION_SUCCESSFUL = 'popups/plant-extension/successful',
  PLANT_UPDATE_CANCELLED = 'popups/plant-edit/cancelled',
  PLANT_UPDATE_CHANGE = 'popups/plant-edit/change',
  PLANT_UPDATE_FAILURE = 'popups/plant-edit/failure',
  PLANT_UPDATE_POPUP_HANDLING_ADD_PLANT_VIEW = 'popups/plant-edit/handling-add-plant-view',
  PLANT_UPDATE_REQUEST = 'popups/plant-edit/request',
  PLANT_UPDATE_SUCCESSFUL = 'popups/plant-edit/successful',
  REJECT_FAILURE = 'popups/plant-reject/failure',
  REJECT_REQUEST = 'popups/plant-reject/request',
  REJECT_SUCCESSFUL = 'popups/plant-reject/successful',
  SWITCH_PLANT_UPDATE_FORM = 'popups/plant-edit/switch-plant-update-form',
  CLOSE_POPUPS = 'popups/close-all',
  CHECK_FOR_ADDITIONAL_EDITS = 'popups/assignment/check-for-additional-edits',
  PLANT_EXTENSION_DESC_ERROR = 'popups/plant-extension/error/description',
  PLANT_EXTENSION_DESC_ERROR_END = 'popups/plant-extension/error/description/end',

  OPEN_HISTORY_POPUP = 'popups/open-material-history-popup',
  OPEN_HISTORY_IN_PROGRESS_POPUP = 'popups/open-material-history-in-progress-popup',
  REQUEST_MATERIAL_HISTORY = 'popups/request-material-history',
  REQUEST_MATERIAL_HISTORY_IN_PROGRESS = 'popups/request-material-history-in-progress',
  REQUEST_MATERIAL_HISTORY_SUCCESS = 'popups/request-material-history-success',
  REQUEST_MATERIAL_HISTORY_FAILURE = 'popups/request-material-history-failure',
  SET_TAB = 'popups/set-tab',
  PROCESS_DETAILS_REQUEST = 'popups/process-details-request',
  PROCESS_DETAILS_SUCCESS = 'popups/process-details-success',
  PROCESS_DETAILS_FAILURE = 'popups/process-details-failure',

  // EXTENTION

  LOAD_STATUS_BY_CLIENTS = 'popups/LoadStatusByClients',
  LOAD_STATUS_BY_CLIENTS_REQUEST = 'popups/LoadStatusByClientsRequest',
  LOAD_STATUS_BY_CLIENTS_SUCCESSFUL = '✔️ popups/LoadStatusByClientsSuccessful',
  LOAD_STATUS_BY_CLIENTS_FAILURE = '❌ popups/LoadStatusByClientsFailure',

  SET_CLIENT = '✍ popups/SetClient',

  GET_PLANT_LANGUAGES_REQUEST = 'get-plant-languages-request',
  GET_PLANT_LANGUAGES_SUCCESS = 'get-plant-languages-success',
  GET_PLANT_LANGUAGES_FAILURE = 'get-plant-languages-failure',

  GET_PLANT_LANG_EXTEND_REQUEST = 'get-plant-lang-extend-request',
  GET_PLANT_LANG_EXTEND_SUCCESS = 'get-plant-lang-extend-success',
  GET_PLANT_LANG_EXTEND_FAILURE = 'get-plant-lang-extend-failure',

  UPDATE_PLANT_LANG_EXTEND = 'popup/update-plant-lang-extend',
  RESET_PLANT_LANG_EXTEND = 'popup/reset-plant-lang-extend',
  ADD_PLANT_LANG_TO_CACHE = 'popup/add-plant-lang-to-cache',

  // Approval

  GET_PLANT_CONFIGURATION_REQUEST = 'get-plant-configuration-request',
  GET_PLANT_CONFIGURATION_SUCCESSFUL = 'get-plant-configuration-successful',
  GET_PLANT_CONFIGURATION_FAILURE = 'get-plant-configuration-failure',


  UPDATE_PLANT_EXTENSION_AFTER_SET_VALUE_SUCCESS = '[plant-extension] update-plant-extension-after-set-value-success',
  UPDATE_PLANT_EXTENSION_AFTER_SET_VALUE_REQUEST = '[plant-extension] update-plant-extension-after-set-value-request',
  UPDATE_PLANT_EXTENSION_AFTER_SET_VALUE_FAILURE = '[plant-extension] update-plant-extension-after-set-value-failure',

  PLANT_UPDATE_CONFIG_PLANT_REQUEST = '[plant-update][ConfigPlant][Request]',
  PLANT_UPDATE_CONFIG_PLANT_SUCCESS = '[plant-update][ConfigPlant][Success]',
  PLANT_UPDATE_CONFIG_PLANT_FAILURE = '[plant-update][ConfigPlant][Failure]',
  PLANT_UPDATE_CONFIG_PLANT_CLEAR = '[plant-update][ConfigPlant][CLEAR]',

  UPDATE_PLANT_EXTENSION_UPDATE_AFTER_SET_VALUE_SUCCESS = '[plant-update] update-plant-extension-update-after-set-value-success',
  UPDATE_PLANT_EXTENSION_UPDATE_AFTER_SET_VALUE_REQUEST = '[plant-update] update-plant-extension-update-after-set-value-request',
  UPDATE_PLANT_EXTENSION_UPDATE_AFTER_SET_VALUE_FAILURE = '[plant-update] update-plant-extension-update-after-set-value-failure',

  UPDATE_PLANT_APPROVAL_AFTER_SET_VALUE_SUCCESS = '[plant-approval] update-plant-approval-after-set-value-success',
  UPDATE_PLANT_APPROVAL_AFTER_SET_VALUE_REQUEST = '[plant-approval] update-plant-approval-after-set-value-request',
  UPDATE_PLANT_APPROVAL_AFTER_SET_VALUE_FAILURE = '[plant-approval] update-plant-approval-after-set-value-failure',

  PLANT_APPROVAL_CONFIG_SUCCESS = '[plant-approval][CONFIG][SUCCESS]',
  PLANT_APPROVAL_CONFIG_REQUEST = '[plant-approval][CONFIG][REQUEST]',
  PLANT_APPROVAL_CONFIG_FAILURE = '[plant-approval][CONFIG][FAILURE]',
  PLANT_APPROVAL_CONFIG_MATERIAL_DETAIL_LOAD_REQUEST = '[plant-approval][CONFIG][DETAIL_LOAD][REQUEST]',
  PLANT_APPROVAL_CONFIG_MATERIAL_DETAIL_LOAD_SUCCESS = '[plant-approval][CONFIG][DETAIL_LOAD][SUCCESS]',

  PLANT_APPROVAL_ROW_EDIT_RESET = '[plant-approval] plant-approval-row-edit-reset',

  OPEN_NOTES_HISTORY_POPUP = 'popups/open-notes-history-popup',
  CLOSE_NOTES_HISTORY_POPUP = 'popups/close-notes-history-popup',
  REQUEST_NOTES_HISTORY = 'popups/request-notes-history',
  REQUEST_TASK_NOTES_HISTORY_SUCCESS = "popups/request-notes-history-success",

}

export interface Material {
  materialId: string;
  client: string;
  materialCode: string;
  description: string;
  completeness?: string;
  indicator?: string;
  role?: string;
}

export interface Assignment {
  materialId: string;
  plants: string[];
}

export interface AssignMaterialRequest {
  assign: Array<Assignment>;
  parentProcessId?: string;
  note: string;
  force?: boolean;
}

export interface ProcessMaterialAssigned {
  processId: string;
  materialIds: string[];
}

export interface MaterialsAssigned {
  processId: string;
  materialId: string;
}

export interface MaterialsNotAssigned {
  materialId: string;
  client: string;
  materialCode: string;
  existingEditProcessList?: Array<ExistingEditProcess>;
}

export interface ExistingEditProcess {
  processId: string;
  patchWithoutAssignment: boolean;
  createdNote: string;
}

export interface AssignMaterialResponse {
  materialsAssigned: Array<MaterialsAssigned>;
  materialsNotAssigned: Array<MaterialsNotAssigned>;
}

export interface IAssigmentSuggestions {
  groups: Array<IGroup>;
  plantlessMaterials?: Array<Material>;
}

export interface IGroup {
  materials: Array<Material>;
  plants?: string[];
}

export class Group {
  constructor(group: IGroup) {
    this.materials = group.materials;
    this.plants = group.plants;
    this.hasMaterialSelected = this.materials.length > 0;
  }

  materials: Array<Material>;
  plants: string[];
  hasMaterialSelected: boolean;
}

export interface ClientErrors {
  [client: string]: string;
}

export interface RelationshipMaterialInfo {
  materialId: string;
  client: string;
  materialCode: string;
  completeness: string;
  description: string;
  stockAmount?: number;
  consumptionAmount?: number;
  orderedAmount?: number;
  primary?: boolean;
  materialRelationship?: RelationshipsForMaterial;
  selected?: boolean;
  stockQuantity?: number;
  consumptionQuantity?: number;
  orderedQuantity?: number;
  baseUnitOfMeasurement?: string;
  status?: string;
  statusDescription?: string;
  obsolete?: boolean;
  isTopStockValue?: boolean;
  isTopConsumptionValue?: boolean;
  isTopOrderedValue?: boolean;
  isGoldenRecord?: boolean;
}

export interface RelationshipsForMaterial {
  materialId: string;
  materialRelationshipsDetails: Array<MaterialRelationshipsDetails>;
}

export interface MaterialRelationshipsDetails {
  relationshipId: string;
  relationshipRole: RelationshipRole;
  relationshipType: RelationshipType;
}

export enum RelationshipRole {
  PRIMARY = 'PRIMARY',
  SECONDARY = 'SECONDARY',
  EQUIVALENT = 'EQUIVALENT',
  CAN_SUBSTITUTE = 'CAN_SUBSTITUTE',
  CAN_BE_SUBSTITUTED_BY = 'CAN_BE_SUBSTITUTED_BY',
}

export enum RelationshipType {
  DUPLICATE = 'DUPLICATE',
  EQUIVALENCE = 'EQUIVALENCE',
  INTERCHANGEABLE = 'INTERCHANGEABLE',
  NORELATIONSHIP = 'NORELATIONSHIP'
}

export interface RelationshipPopupData {
  materials: Array<RelationshipMaterialInfo>;
}

export interface RelationshipValidateDraftResponse {
  crossClient: boolean;
  relationshipValidateDrafts: Array<RelationshipValidateDraft>;
  clientValidation: ClientErrors;
}

export interface MaterialInRelationship {
  materialId: string;
  role: string;
  materialStatus?: string;
}

export interface CreateRelationshipRequest {
  relationshipType: string;
  materialsInRelationship: Array<MaterialInRelationship>;
  note: string;
  parentProcessId?: string;
  creatingGoldenRecord?: boolean;
}

export interface ClientProcessAndRelationshipResponse {
  processId: string;
  relationshipId: string;
  client: string;
  errorMessage: string;
}

export interface CreateRelationshipResponse {
  clientsResponse: Array<ClientProcessAndRelationshipResponse>;
}

export interface CreateRelationshipResults {
  request: CreateRelationshipRequest;
  response: CreateRelationshipResponse;
}

export interface RelationshipValidateDraft {
  materialId: string;
  materialCode: string;
  client: string;
  errorMessage: string;
}

export interface PlantExtension {
  code: string;
  description: string;
}

export interface PlantExtensionAppliedEditsTableRow {
  attribute: string;
  editType: string;
  previousValue: string;
  currentValue: string;
}

export interface MaterialPlantData {
  client: string;
  mdDomain?: MDDomain;
  materialCode: string;
  materialId: string;
  plantCode: string[];
  crossPlantMaterialStatus: string;
  crossPlantPurchasingGroup: string;
  grInstances: { [client: string]: SimplifiedMaterial };
  shortDescription: string;
  shortDescriptions: { [key: string]: string };
  purchaseOrderDescriptions: { [key: string]: string };
  goldenRecord: string;
  technicalClassification: CategoryKey;
  configuration?: Array<ItemTab>;
}

export interface PlantLanguage {
  client: string;
  plantCode: string;
  language: string;
}

export interface PlantLanguageResponse {
  plantLanguages: Array<PlantLanguage>;
  distinctLanguages: string[];
}

export interface PlantLanguageRequest {
  client: string;
  enabled?: boolean;
  codes: string[];
}

export interface PlantValuesRequest {
  codeText: string;
  client: string | string[];
  descriptionText: string;
  itemPerPage: number;
  page: number;
}

export interface PlantValuesResults {
  plants: Array<Plant>;
  totalNumberOfResult: number;
}

export interface Plant {
  client: string;
  code: string;
  countryCode: string;
  description: string;
  currency: string;
}

export interface PlantBasic {
  client: string;
  code: string;
}

export interface LocalizedFieldValue {
  key: string;
  text: string;
  label?: string;
}

export interface IMaterialStorageLocationDetails {
  storageLocation: string;
  valuatedUnrestrictedUseStock: number;
  stockInTransfer: number;
  stockInQualityInspection: number;
  blockedStock: number;
  storageBin: string;
}

export class MaterialStorageLocationDetails
  implements IMaterialStorageLocationDetails {
  storageLocation: string;
  valuatedUnrestrictedUseStock: number;
  stockInTransfer: number;
  stockInQualityInspection: number;
  blockedStock: number;
  storageBin: string;
}

export interface PlantKey {
  code: string;
  client: string;
  description?: string;
}

export interface GetExtensionPayloadData {
  newValue: any;
  attributeName: string;
  materialId: string;
  plantExtensionDetail: PlantExtensionDetail;
}

export interface GetPlantApprovalConfigData {
  masterId: string,
  processId: string,
  plantCode?: string
  page: string
}

export interface GetPlantUpdatePayloadData {
  newValue: any;
  attributeName: string;
  materialId: string;
  processId?: string;
  plantUpdateDetail: PlantUpdateDetail;
}

export interface GetExtensionEditorRequest {
  page: string;
  language: string;
  value: any; // -> can be only a string or a list for alternativeunitofmeasure
  attributeName: string;
  materialDetails: MaterialDetails;
  plantDetails: PlantExtensionDetail[];
  fallbackLanguages: Array<string>;
  client?: string;
  descriptionLanguage: string;
}

export interface GetPlantUpdateEditorRequest {
  page: string;
  language: string;
  value: any; // -> can be only a string or a list for alternativeunitofmeasure
  attributeName: string;
  materialDetails: MaterialDetails;
  plantExtensionDetailChanges: PlantUpdateDetail[];
  fallbackLanguages: Array<string>;
  client?: string;
  descriptionLanguage: string;
}

export interface GetExtensionEditorResponse {
  tabs: ItemTab[];
  formState: ValidateUpdatedMaterialResponse | FormState;
  plantDetails: PlantExtensionDetail[];
}

export interface GetPlantUpdateEditorResponse {
  tabs: ItemTab[];
  formState: ValidateUpdatedMaterialResponse | FormState;
  plantExtensionDetailChanges: PlantUpdateDetail[];
}

export interface IPlantExtensionDetail {
  plantCode: string;
  plantDescription: string;
  mrpType: string;
  mrpGroup: string;
  mrpController: string;
  materialStatus: string;
  purchasingGroup: string;
  logisticGroup: string;
  leadTimeInDays: number;
  reorderPoint: number;
  safetyStock: number;
  minSafetyStock: number;
  maxSafetyStock: number;
  currency: string;
  standardPrice: number;
  priceUnit: number;
  lotSize: string;
  minLotSize: number;
  supportsMultipleValuations: boolean;
  availableStorages: string[];
  storageBins?: Array<{ key: string, values: string[] }>;
  client: string;
  intrastatCode: string;
  controlCodeConsumptionTaxesForeignTrade: string;
  materialCFOPCategory: string;
  periodIndicator: string;
  procurementType: string;
  checkingGroupAvailabilityCheck: string;
  certificateType: string;
  profitCenter: string;
  plantOldMaterialNumber: string;
  lotSizeForProductCosting: number;
  schedulingMarginKeyForFloats: string;
}

export interface IMaterialPlantExtension {
  materialPlantDetails: Array<IPlantExtensionDetail>;
  materialId: string;
  createGoldenRecord?: boolean;
  shortDescriptions?: { [key: string]: string };
  purchaseOrderDescriptions?: { [key: string]: string };
  language?: string;
  requesterNote?: string;

}

export enum RelationshipTypes {
  equivalence = 'equivalence',
  interchangeable = 'interchangeable',
  duplicate = 'duplicate',
  noRelationship = 'noRelationship'
}

export enum MDDomain {
  MATERIALS = 'M',
  SERVICES = 'S',
}

export interface ProcessCodeResponse {
  processCode: string;
}

export interface DeduplicateProcessCodeResponse {
  processCode: Array<String>;
}

export class PlantExtensionDetail implements IPlantExtensionDetail {
  plantCode: string;
  plantDescription: string;
  mrpType: string;
  mrpGroup: string;
  mrpController: string;
  materialStatus: string;
  purchasingGroup: string;
  logisticGroup: string;
  leadTimeInDays: number;
  reorderPoint: number;
  safetyStock: number;
  minSafetyStock: number;
  maxSafetyStock: number;
  currency: string;
  standardPrice: number;
  priceUnit: number;
  lotSize: string;
  minLotSize: number;
  supportsMultipleValuations: boolean;
  availableStorages: string[];
  storageBins?: Array<{ key: string, values: string[] }>;
  client: string;
  intrastatCode: string;
  controlCodeConsumptionTaxesForeignTrade: string;
  materialCFOPCategory: string;
  periodIndicator: string;
  procurementType: string;
  checkingGroupAvailabilityCheck: string;
  certificateType: string;
  profitCenter: string;
  plantOldMaterialNumber: string;
  lotSizeForProductCosting: number;
  schedulingMarginKeyForFloats: string;

  constructor() {
    /*    this.leadTimeInDays = 0;
        this.maxSafetyStock = 0;
        this.minSafetyStock = 0;
        this.safetyStock = 0;
        this.reorderPoint = 0;
        this.standardPrice = 0;*/
  }

}

export class OpenAssignmentPopup implements Action {
  readonly type = PopupActionTypes.OPEN_ASSIGNMENT_POPUP;

  constructor(
    public payload: { materials: string[]; parentProcessId?: string }) {
  }
}

export class AssignmentRequest implements Action {
  readonly type = PopupActionTypes.ASSIGNMENT_REQUEST;

  constructor(public payload: { groups: AssignMaterialRequest }) {
  }
}

export class AssignmentCancelled implements Action {
  readonly type = PopupActionTypes.ASSIGNMENT_CANCELLED;

  constructor() {
  }
}

export class AssignmentSuccessful implements Action {
  readonly type = PopupActionTypes.ASSIGNMENT_SUCCESSFUL;

  constructor(public payload: Array<ProcessMaterialAssigned>) {
  }
}

export class AssignmentFailure implements Action {
  readonly type = PopupActionTypes.ASSIGNMENT_FAILURE;

  constructor(public error: HttpErrorResponse) {
  }
}

export class CheckForAdditionalEdits implements Action {
  readonly type = PopupActionTypes.CHECK_FOR_ADDITIONAL_EDITS;

  constructor(public payload: AssignMaterialResponse) {
  }
}

export class OpenCreateRelationshipPopup implements Action {
  readonly type = PopupActionTypes.OPEN_RELATIONSHIP_CREATE_POPUP;

  constructor(
    public payload: { materialIds: string[]; parentProcessId?: string }) {
  }
}

export class OpenPlantExtensionPopup implements Action {
  readonly type = PopupActionTypes.OPEN_PLANT_EXTENSION_POPUP;

  constructor(public payload: { materialId: string }) {
  }
}

export class ClosePlantExtensionPopup implements Action {
  readonly type = PopupActionTypes.CLOSE_PLANT_EXTENSION_POPUP;

  constructor() {
  }
}

export class PlantExtensionHandlingAddPlantView implements Action {
  readonly type =
    PopupActionTypes.PLANT_EXTENSION_POPUP_HANDLING_ADD_PLANT_VIEW;

  constructor(
    public payload: { plantExtensionDetail: IPlantExtensionDetail }) {
  }
}

export class EditPlantExtensionRequest implements Action {
  readonly type = PopupActionTypes.EDIT_PLANT_EXTENSION_REQUEST;

  constructor(
    public payload: {
      isEdit: boolean;
      plantExtensionDetail: IPlantExtensionDetail;
    }) {
  }
}

export class EditPlantExtensionSuccessful implements Action {
  readonly type = PopupActionTypes.EDIT_PLANT_EXTENSION_SUCCESSFUL;

  constructor(
    public payload: { plantExtensionDetail: IPlantExtensionDetail }) {
  }
}

export class DeletePlantExtensionRequest implements Action {
  readonly type = PopupActionTypes.DELETE_PLANT_EXTENSION_REQUEST;

  constructor(
    public payload: { plantExtensionDetail: IPlantExtensionDetail }) {
  }
}

export class DeletePlantExtensionSuccessful implements Action {
  readonly type = PopupActionTypes.DELETE_PLANT_EXTENSION_SUCCESSFUL;

  constructor(
    public payload: { plantExtensionDetail: IPlantExtensionDetail }) {
  }
}

export class PlantExtensionRequest implements Action {
  readonly type = PopupActionTypes.PLANT_EXTENSION_REQUEST;

  public constructor(
    public payload: { materialPlantExtension: IMaterialPlantExtension }) {
  }
}

export class PlantExtensionSuccessful implements Action {
  readonly type = PopupActionTypes.PLANT_EXTENSION_SUCCESSFUL;

  public constructor() {
  }
}

export class PlantExtensionFailure implements Action {
  readonly type = PopupActionTypes.PLANT_EXTENSION_FAILURE;

  constructor(public error: HttpErrorResponse) {
  }
}

export class PlantExtensionCancelled implements Action {
  readonly type = PopupActionTypes.PLANT_EXTENSION_CANCELLED;

  constructor() {
  }
}

export class PlantExtensionChange implements Action {
  readonly type = PopupActionTypes.PLANT_EXTENSION_CHANGE;

  constructor(public payload: { plant: Plant }) {
  }
}

export class OpenPlantUpdatePopup implements Action {
  readonly type = PopupActionTypes.OPEN_PLANT_UPDATE_POPUP;

  constructor(public payload: { materialId: string }) {
  }
}

export class ClosePlantUpdatePopup implements Action {
  readonly type = PopupActionTypes.CLOSE_PLANT_UPDATE_POPUP;

  constructor() {
  }
}

export class PlantUpdateHandlingAddPlantView implements Action {
  readonly type = PopupActionTypes.PLANT_UPDATE_POPUP_HANDLING_ADD_PLANT_VIEW;

  constructor(public payload: { plantUpdateDetail: IPlantUpdateDetail }) {
  }
}

export class EditPlantUpdateRequest implements Action {
  readonly type = PopupActionTypes.EDIT_PLANT_UPDATE_REQUEST;

  constructor(
    public payload: { isEdit: boolean; plantUpdateDetail: IPlantUpdateDetail }) {
  }
}

export class EditPlantUpdateSuccessful implements Action {
  readonly type = PopupActionTypes.EDIT_PLANT_UPDATE_SUCCESSFUL;

  constructor(public payload: { response: EditPlantUpdateRequestPayload }) {
  }
}

export class DeletePlantUpdateRequest implements Action {
  readonly type = PopupActionTypes.DELETE_PLANT_UPDATE_REQUEST;

  constructor(public payload: { plantUpdateDetail: IPlantUpdateDetail }) {
  }
}

export class DeletePlantUpdateSuccessful implements Action {
  readonly type = PopupActionTypes.DELETE_PLANT_UPDATE_SUCCESSFUL;

  constructor(public payload: { plantUpdateDetail: IPlantUpdateDetail }) {
  }
}

export class PlantUpdateRequest implements Action {
  readonly type = PopupActionTypes.PLANT_UPDATE_REQUEST;

  public constructor(public payload: PlantUpdateRequestPayload) {
  }
}

export class PlantUpdateSuccessful implements Action {
  readonly type = PopupActionTypes.PLANT_UPDATE_SUCCESSFUL;

  public constructor() {
  }
}

export class PlantUpdateFailure implements Action {
  readonly type = PopupActionTypes.PLANT_UPDATE_FAILURE;

  constructor(public error: HttpErrorResponse, public response?: PlantValidationErrors) {
  }
}

export class PlantUpdateCancelled implements Action {
  readonly type = PopupActionTypes.PLANT_UPDATE_CANCELLED;

  constructor() {
  }
}

export class PlantUpdateChange implements Action {
  readonly type = PopupActionTypes.PLANT_UPDATE_CHANGE;

  constructor(public payload: { plant: Plant }) {
  }
}

export class GetOriginalMaterialPlantUpdateRequest implements Action {
  readonly type = PopupActionTypes.GET_ORIGINAL_MATERIAL_PLANT_UPDATE_REQUEST;

  constructor(public payload: { materialId: string }) {
  }
}

export class GetOriginalMaterialPlantUpdateSuccessful implements Action {
  readonly type =
    PopupActionTypes.GET_ORIGINAL_MATERIAL_PLANT_UPDATE_SUCCESSFUL;

  public constructor(public payload: { result: MaterialPlantData }) {
  }
}

export class GetOriginalMaterialPlantUpdateFailure implements Action {
  readonly type = PopupActionTypes.GET_ORIGINAL_MATERIAL_PLANT_UPDATE_FAILURE;

  constructor(public error: HttpErrorResponse) {
  }
}

export class GetOriginalMaterialRequest implements Action {
  readonly type = PopupActionTypes.GET_ORIGINAL_MATERIAL_REQUEST;

  constructor(public payload: { materialId: string }) {
  }
}

export class GetOriginalMaterialSuccessful implements Action {
  readonly type = PopupActionTypes.GET_ORIGINAL_MATERIAL_SUCCESSFUL;

  public constructor(
    public payload: { result: MaterialPlantData },
    public materialId: string,) {
  }
}

export class GetOriginalMaterialFailure implements Action {
  readonly type = PopupActionTypes.GET_ORIGINAL_MATERIAL_FAILURE;

  constructor(public error: HttpErrorResponse) {
  }
}

export class GetAvailableClientsRequest implements Action {
  readonly type = PopupActionTypes.GET_AVAILABLE_CLIENTS_REQUEST;

  constructor(public payload: { materialId: string }) {
  }
}

export class GetAvailableClientsSuccessful implements Action {
  readonly type = PopupActionTypes.GET_AVAILABLE_CLIENTS_SUCCESSFUL;

  constructor(public otherAvailableClients: string[]) {
  }
}

export class GetAvailableClientsFailure implements Action {
  readonly type = PopupActionTypes.GET_AVAILABLE_CLIENTS_FAILURE;

  constructor(public error: HttpErrorResponse) {
  }
}

export class GetMaterialPlants implements Action {
  readonly type = PopupActionTypes.GET_MATERIAL_PLANTS;

  constructor(public payload: { materialId: string }) {
  }
}

export class GetMaterialPlantsSuccessful implements Action {
  readonly type = PopupActionTypes.GET_MATERIAL_PLANTS_SUCCESSFUL;

  public constructor(
    public payload: { result: Array<IPlantExtensionDetails> }) {
  }
}

export class GetMaterialPlantsFailure implements Action {
  readonly type = PopupActionTypes.GET_MATERIAL_PLANTS_FAILURE;

  constructor(public error: HttpErrorResponse) {
  }
}

export class GetPlantsRequest implements Action {
  readonly type = PopupActionTypes.GET_PLANTS_REQUEST;

  constructor() {
  }
}

export class GetPlantsSuccessful implements Action {
  readonly type = PopupActionTypes.GET_PLANTS_SUCCESSFUL;

  public constructor(public payload: { result: Array<Plant> }) {
  }
}

export class GetPlantsFailure implements Action {
  readonly type = PopupActionTypes.GET_PLANTS_FAILURE;

  constructor(public error: HttpErrorResponse) {
  }
}


export class GetPlantsUpdateRequest implements Action {
  readonly type = PopupActionTypes.GET_PLANTS_UPDATE_REQUEST;

  constructor() {
  }
}

export class GetPlantsUpdateSuccessful implements Action {
  readonly type = PopupActionTypes.GET_PLANTS_UPDATE_SUCCESSFUL;

  public constructor(public payload: { result: Array<Plant> }) {
  }
}

export class GetPlantsUpdateFailure implements Action {
  readonly type = PopupActionTypes.GET_PLANTS_UPDATE_FAILURE;

  constructor(public error: HttpErrorResponse) {
  }
}


export class SwitchPlantUpdateForm implements Action {
  readonly type = PopupActionTypes.SWITCH_PLANT_UPDATE_FORM;

  constructor() {
  }
}

export class CloseAllPopups implements Action {
  readonly type = PopupActionTypes.CLOSE_POPUPS;

  constructor() {
  }
}

export class OpenHistoryPopup implements Action {
  readonly type = PopupActionTypes.OPEN_HISTORY_POPUP;

  constructor(public payload: { materialId: string }) {
  }
}

export class OpenHistoryInProgressPopup implements Action {
  readonly type = PopupActionTypes.OPEN_HISTORY_IN_PROGRESS_POPUP;

  constructor(public payload: { materialId: string }) {
  }
}

export class RequestMaterialHistory implements Action {
  readonly type = PopupActionTypes.REQUEST_MATERIAL_HISTORY;

  constructor(public payload: { request: MaterialHistoryProcessesRequest }) {
  }
}

export class RequestMaterialHistoryInProgress implements Action {
  readonly type = PopupActionTypes.REQUEST_MATERIAL_HISTORY_IN_PROGRESS;

  constructor(public payload: { request: MaterialHistoryProcessesRequest }) {
  }
}

export class RequestMaterialHistorySuccess implements Action {
  readonly type = PopupActionTypes.REQUEST_MATERIAL_HISTORY_SUCCESS;

  constructor(
    public payload: { materialId: string; history: MaterialHistory }) {
  }
}

export class RequestMaterialHistoryFailure implements Action {
  readonly type = PopupActionTypes.REQUEST_MATERIAL_HISTORY_FAILURE;

  constructor(public readonly payload: { error: string }) {
  }
}

export class SetTab implements Action {
  readonly type = PopupActionTypes.SET_TAB;

  constructor(public tab: TabName) {
  }
}

export class ProcessDetailsRequest implements Action {
  readonly type = PopupActionTypes.PROCESS_DETAILS_REQUEST;

  constructor(public payload: { processId: string }) {
  }
}

export class ProcessDetailsSuccess implements Action {
  readonly type = PopupActionTypes.PROCESS_DETAILS_SUCCESS;

  constructor(public payload: { processDetails: ProcessDetails }) {
  }
}

export class ProcessDetailsFailure implements Action {
  readonly type = PopupActionTypes.PROCESS_DETAILS_FAILURE;

  constructor(public payload: { error: string }) {
  }
}

// component dispatch LoadStatusByClients, effects fills in parameters and dispatch: LoadStatusByClientsRequest
// another fx calls the backend and dispatch: LoadStatusByClientsSuccessful / LoadStatusByClientsFailure

export class LoadStatusByClients implements Action {
  readonly type = PopupActionTypes.LOAD_STATUS_BY_CLIENTS;

  constructor() {
  }
}

export class LoadStatusByClientsRequest implements Action {
  readonly type = PopupActionTypes.LOAD_STATUS_BY_CLIENTS_REQUEST;

  constructor(public clients: string[], public language: string) {
  }
}

export class LoadStatusByClientsSuccessful implements Action {
  readonly type = PopupActionTypes.LOAD_STATUS_BY_CLIENTS_SUCCESSFUL;

  constructor(public availableStatusByClient: ClientStatuses) {
  }
}

export class LoadStatusByClientsFailure implements Action {
  readonly type = PopupActionTypes.LOAD_STATUS_BY_CLIENTS_FAILURE;

  constructor(public payload: { error: string }) {
  }
}

export class SetClient implements Action {
  readonly type = PopupActionTypes.SET_CLIENT;

  constructor(public client: string) {
  }
}

export class GetPlantLanguagesRequest implements Action {
  readonly type = PopupActionTypes.GET_PLANT_LANGUAGES_REQUEST;

  constructor(public payload: PlantLanguageRequest) {
  }
}

export class GetPlantLanguagesSuccess implements Action {
  readonly type = PopupActionTypes.GET_PLANT_LANGUAGES_SUCCESS;

  constructor(public payload: PlantLanguageResponse) {
  }
}

export class GetPlantLanguagesFailure implements Action {
  readonly type = PopupActionTypes.GET_PLANT_LANGUAGES_FAILURE;

  constructor(public payload: { error: string }) {
  }
}

export class GetPlantLangExtendRequest implements Action {
  readonly type = PopupActionTypes.GET_PLANT_LANG_EXTEND_REQUEST;

  constructor(public payload: PlantLanguageRequest) {
  }
}

export class GetPlantLangExtendSuccess implements Action {
  readonly type = PopupActionTypes.GET_PLANT_LANG_EXTEND_SUCCESS;

  constructor(public payload: PlantLanguageResponse) {
  }
}

export class GetPlantLangExtendFailure implements Action {
  readonly type = PopupActionTypes.GET_PLANT_LANG_EXTEND_FAILURE;

  constructor(public payload: { error: string }) {
  }
}

export class UpdatePlantLangExtend implements Action {
  readonly type = PopupActionTypes.UPDATE_PLANT_LANG_EXTEND;

  constructor(public payload: { client: string, language: string }) {
  }
}

export class ResetPlantLangExtend implements Action {
  readonly type = PopupActionTypes.RESET_PLANT_LANG_EXTEND;

  constructor() {
  }
}

export class PlantExtensionDescError implements Action {
  readonly type = PopupActionTypes.PLANT_EXTENSION_DESC_ERROR;

  constructor(public payload: { error: string, tasksLoading: [], errorDesc: { field: string, language: string }[] }) {
  }
}

export class PlantExtensionDescErrorEnd implements Action {
  readonly type = PopupActionTypes.PLANT_EXTENSION_DESC_ERROR_END;

  constructor(public payload: { error: string, errorDesc: { field: string, language: string }[] }) {
  }
}

export class AddPlantLangToCache implements Action {
  readonly type = PopupActionTypes.ADD_PLANT_LANG_TO_CACHE;

  constructor(public payload: Array<{
    lang: 'string'
    shortDescription: string,
    longDescription: string
  }>) {
  }
}


export class GetPlantConfigurationsRequest implements Action {
  readonly type = PopupActionTypes.GET_PLANT_CONFIGURATION_REQUEST;

  constructor(public page: string, public masterId: string, public plantCode?: string) {
  }
}

export class GetPlantConfigurationsSuccessful implements Action {
  readonly type = PopupActionTypes.GET_PLANT_CONFIGURATION_SUCCESSFUL;

  public constructor(public payload: ItemDetailsAttributes) {
  }
}

export class GetPlantConfigurationsFailure implements Action {
  readonly type = PopupActionTypes.GET_PLANT_CONFIGURATION_FAILURE;

  constructor(public error: HttpErrorResponse) {
  }
}


export class UpdatePlantExtensionAfterSetValueRequest implements Action {
  readonly type = PopupActionTypes.UPDATE_PLANT_EXTENSION_AFTER_SET_VALUE_REQUEST;

  constructor(public payload: GetExtensionPayloadData) {
  }
}

export class UpdatePlantExtensionAfterSetValueSuccess implements Action {
  readonly type = PopupActionTypes.UPDATE_PLANT_EXTENSION_AFTER_SET_VALUE_SUCCESS;

  constructor(public payload: GetExtensionEditorResponse) {
  }
}

export class UpdatePlantExtensionAfterSetValueFailure implements Action {
  readonly type = PopupActionTypes.UPDATE_PLANT_EXTENSION_AFTER_SET_VALUE_FAILURE;

  constructor(public readonly payload: { errorMessage: string }) {
  }
}


export class UpdatePlantExtensionUpdateAfterSetValueRequest implements Action {
  readonly type = PopupActionTypes.UPDATE_PLANT_EXTENSION_UPDATE_AFTER_SET_VALUE_REQUEST;

  constructor(public payload: GetPlantUpdatePayloadData) {
  }
}

export class UpdatePlantExtensionUpdateAfterSetValueSuccess implements Action {
  readonly type = PopupActionTypes.UPDATE_PLANT_EXTENSION_UPDATE_AFTER_SET_VALUE_SUCCESS;

  constructor(public payload: GetPlantUpdateEditorResponse) {
  }
}

export class UpdatePlantExtensionUpdateAfterSetValueFailure implements Action {
  readonly type = PopupActionTypes.UPDATE_PLANT_EXTENSION_UPDATE_AFTER_SET_VALUE_FAILURE;

  constructor(public readonly payload: { errorMessage: string }) {
  }
}

export class UpdatePlantApprovalAfterSetValueRequest implements Action {
  readonly type = PopupActionTypes.UPDATE_PLANT_APPROVAL_AFTER_SET_VALUE_REQUEST;

  constructor(public payload: GetPlantUpdatePayloadData) {
  }
}

export class UpdatePlantApprovalAfterSetValueSuccess implements Action {
  readonly type = PopupActionTypes.UPDATE_PLANT_APPROVAL_AFTER_SET_VALUE_SUCCESS;

  constructor(public payload: GetPlantUpdateEditorResponse) {
  }
}

export class UpdatePlantApprovalAfterSetValueFailure implements Action {
  readonly type = PopupActionTypes.UPDATE_PLANT_APPROVAL_AFTER_SET_VALUE_FAILURE;

  constructor(public readonly payload: { errorMessage: string }) {
  }
}


export class PlantApprovalConfigRequest implements Action {
  readonly type = PopupActionTypes.PLANT_APPROVAL_CONFIG_REQUEST;

  constructor(public payload: GetPlantApprovalConfigData) {
  }
}

export class PlantApprovalConfigSuccess implements Action {
  readonly type = PopupActionTypes.PLANT_APPROVAL_CONFIG_SUCCESS;

  constructor(public payload: GetPlantUpdateEditorResponse) {
  }
}

export class PlantApprovalConfigFailure implements Action {
  readonly type = PopupActionTypes.PLANT_APPROVAL_CONFIG_FAILURE;

  constructor(public readonly error: HttpErrorResponse) {
  }
}


export class RequestTaskNotesHistorySuccess implements Action {
  readonly type = PopupActionTypes.REQUEST_TASK_NOTES_HISTORY_SUCCESS;

  constructor(
    public payload: { taskId?: number; history?: TaskNotesHistoryResponse, showSidebar: boolean }) {
  }
}

export class OpenNotesHistoryPopup implements Action {
  readonly type = PopupActionTypes.OPEN_NOTES_HISTORY_POPUP;

  constructor(public payload: { taskId?: number, processId?: string }) {
  }
}

export class CloseNotesHistoryPopup implements Action {
  readonly type = PopupActionTypes.CLOSE_NOTES_HISTORY_POPUP;

  constructor() {
  }
}

export class RequestTaskNotesHistory implements Action {
  readonly type = PopupActionTypes.REQUEST_NOTES_HISTORY;

  constructor(public payload: { request: TaskNotesHistoryRequest }) {
  }
}

export class PlantUpdateConfigPlantRequest implements Action {
  readonly type = PopupActionTypes.PLANT_UPDATE_CONFIG_PLANT_REQUEST;

  constructor(public page: string, public masterId: string, public plantCode?: string) {
  }
}

export class PlantUpdateConfigPlantSuccess implements Action {
  readonly type = PopupActionTypes.PLANT_UPDATE_CONFIG_PLANT_SUCCESS;

  public constructor(public payload: ItemDetailsAttributes) {
  }
}

export class PlantUpdateConfigPlantFailure implements Action {
  readonly type = PopupActionTypes.PLANT_UPDATE_CONFIG_PLANT_FAILURE;

  constructor(public error: HttpErrorResponse) {
  }
}

export class PlantUpdateConfigPlantClear implements Action {
  readonly type = PopupActionTypes.PLANT_UPDATE_CONFIG_PLANT_CLEAR;

  constructor() {
  }
}

export class PlantApprovalConfigMaterialDetailLoadRequest implements Action {
  readonly type = PopupActionTypes.PLANT_APPROVAL_CONFIG_MATERIAL_DETAIL_LOAD_REQUEST;

  constructor(public masterdataId: string) {
  }
}

export class PlantApprovalConfigMaterialDetailLoadSuccess implements Action {
  readonly type = PopupActionTypes.PLANT_APPROVAL_CONFIG_MATERIAL_DETAIL_LOAD_SUCCESS;

  constructor(public materialDetails: MaterialDetails) {
  }
}


export type PopupActions =
  | OpenAssignmentPopup
  | AssignmentRequest
  | AssignmentCancelled
  | AssignmentSuccessful
  | AssignmentFailure
  | OpenCreateRelationshipPopup
  | OpenPlantExtensionPopup
  | ClosePlantExtensionPopup
  | PlantExtensionHandlingAddPlantView
  | EditPlantExtensionSuccessful
  | DeletePlantExtensionRequest
  | DeletePlantExtensionSuccessful
  | EditPlantExtensionRequest
  | PlantExtensionRequest
  | PlantExtensionSuccessful
  | PlantExtensionFailure
  | PlantExtensionCancelled
  | PlantExtensionChange
  | OpenPlantUpdatePopup
  | ClosePlantUpdatePopup
  | PlantUpdateHandlingAddPlantView
  | EditPlantUpdateSuccessful
  | DeletePlantUpdateRequest
  | DeletePlantUpdateSuccessful
  | EditPlantUpdateRequest
  | PlantUpdateRequest
  | PlantUpdateSuccessful
  | PlantUpdateFailure
  | PlantUpdateCancelled
  | PlantUpdateChange
  | GetOriginalMaterialPlantUpdateRequest
  | GetOriginalMaterialPlantUpdateSuccessful
  | GetOriginalMaterialPlantUpdateFailure
  | GetOriginalMaterialRequest
  | GetOriginalMaterialSuccessful
  | GetOriginalMaterialFailure
  | GetAvailableClientsRequest
  | GetAvailableClientsFailure
  | GetAvailableClientsSuccessful
  | GetPlantsRequest
  | GetPlantsSuccessful
  | GetPlantsFailure

  | GetMaterialPlants
  | GetMaterialPlantsSuccessful
  | GetMaterialPlantsFailure
  | GetPlantsUpdateRequest
  | GetPlantsUpdateSuccessful
  | GetPlantsUpdateFailure
  | SwitchPlantUpdateForm
  | RejectFailure
  | OpenExtensionApprovalPopup
  | OpenUpdateApprovalPopup
  | GetProcessInfoSuccessful
  | OpenRelationshipApprovalPopup
  | GetProcessInfoRelationshipSuccessful
  | GetProcessInfo
  | GetProcessInfoFailure
  | CloseAllPopups
  | CheckForAdditionalEdits
  | OpenHistoryPopup
  | RequestMaterialHistory
  | RequestMaterialHistorySuccess
  | RequestMaterialHistoryFailure
  | SetTab
  | ProcessDetailsRequest
  | ProcessDetailsSuccess
  | ProcessDetailsFailure
  | LoadStatusByClients
  | LoadStatusByClientsRequest
  | LoadStatusByClientsSuccessful
  | LoadStatusByClientsFailure
  | SetClient
  | GetPlantLanguagesRequest
  | GetPlantLanguagesSuccess
  | GetPlantLanguagesFailure
  | GetPlantLangExtendRequest
  | GetPlantLangExtendSuccess
  | GetPlantLangExtendFailure
  | UpdatePlantLangExtend
  | ResetPlantLangExtend
  | AddPlantLangToCache
  | PlantExtensionDescError
  | PlantExtensionDescErrorEnd
  | GetPlantConfigurationsRequest
  | GetPlantConfigurationsSuccessful
  | GetPlantConfigurationsFailure
  | UpdatePlantExtensionAfterSetValueRequest
  | UpdatePlantExtensionAfterSetValueSuccess
  | UpdatePlantExtensionAfterSetValueFailure
  | UpdatePlantExtensionUpdateAfterSetValueRequest
  | UpdatePlantExtensionUpdateAfterSetValueSuccess
  | UpdatePlantExtensionUpdateAfterSetValueFailure
  | UpdatePlantApprovalAfterSetValueRequest
  | UpdatePlantApprovalAfterSetValueSuccess
  | UpdatePlantApprovalAfterSetValueFailure
  | OpenNotesHistoryPopup
  | CloseNotesHistoryPopup
  | RequestTaskNotesHistory
  | RequestTaskNotesHistorySuccess
  | ResetApprovalEdit
  | PlantUpdateConfigPlantRequest
  | PlantUpdateConfigPlantSuccess
  | PlantUpdateConfigPlantFailure
  | PlantUpdateConfigPlantClear
  | PlantApprovalConfigRequest
  | PlantApprovalConfigSuccess
  | PlantApprovalConfigFailure
  | PlantApprovalConfigMaterialDetailLoadRequest
  | PlantApprovalConfigMaterialDetailLoadSuccess
  ;
